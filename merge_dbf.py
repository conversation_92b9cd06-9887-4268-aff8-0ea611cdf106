"""
同源数据智能识别工具
功能：DBF文件合并 + 中文字段拆分 + 同源数据智能识别 + 风险排查 + 资源监控 + 安全中断 + GUI界面
环境：Python 3.8+ 需安装 pip install dbfread pandas pyarrow psutil PyQt5 tqdm requests
"""

import os
import gc
import time
import sys
import pandas as pd
import psutil
from dbfread import DBF, DBFNotFound
from collections import defaultdict
from typing import Dict
from tqdm import tqdm
import re
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill
import json

# PyQt5 imports
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QLabel, QTextEdit, QProgressBar,
                            QFileDialog, QMessageBox, QTabWidget, QGroupBox,
                            QRadioButton, QButtonGroup)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QTextCursor, QIcon, QPixmap
import logging
import ipaddress
import base64
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# =================== 配置参数 ===================
MEMORY_SAFE_LIMIT = 0.7     # 内存使用安全阈值（总内存占比）
INITIAL_CHUNK_SIZE = 2000   # 初始分块行数（根据内存动态调整）
SPARSE_THRESHOLD = 0.005     # 稀疏列筛选阈值（出现频率低于0.5%忽略）
ENCODING_CANDIDATES = ['gb18030', 'utf_8', 'gbk']  # 中文编码优先级
SPLIT_FIELD = "站点地址"      # 需要拆分的字段名称（中文）

# 智能识别同源数据分析配置
DUPLICATE_CHECK_FIELDS = ["HD", "ICCID", "IDFA", "IDFV", "IIP", "IMEI", "IMSI", "IP", "MAC", "MPN", "PCN", "RMPN", "TEL", "UMPN"]

# 客户身份识别字段配置 - 简化版本
PRIMARY_CUSTOMER_ID_FIELD = "客户编号"  # 主要客户唯一标识符
FALLBACK_CUSTOMER_FIELDS = ["客户姓名", "资产账户"]  # 备用客户识别字段
AMOUNT_FIELDS = ["发生金额", "后资金额"]

# 排除常见误报值配置 - 新增功能
FALSE_POSITIVE_VALUES = {
    "IMSI": ["NA@TYPE=NA", "NA@储宝宝Plus", "NA@TYPE=GM", "NA@TYPE=0", "6553565535"],
    "MAC": ["02:00:00:00:00:00", "00"]
}

# 文件生成和处理配置
MIN_EXCEL_FILE_SIZE = 5 * 1024  # 最小Excel文件大小（5KB）
LARGE_FILE_THRESHOLD = 50 * 1024 * 1024  # 大文件阈值（50MB）
MAX_EXCEL_FILE_SIZE = 100 * 1024 * 1024  # 最大Excel文件大小（100MB）
BATCH_PROCESSING_SIZE = 10000  # 分批处理大小

# 应用程序图标配置
# 用户可以将自己的图标文件转换为base64编码后替换下面的空字符串
# 转换方法：使用在线base64编码工具或Python: base64.b64encode(open('icon.png', 'rb').read()).decode()
BASE64_ICON_DATA = ""  # 在此处粘贴base64编码的图标数据
# ================================================

class GuiLogHandler(logging.Handler):
    """自定义日志处理器，将日志输出到GUI"""
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)
        # 使用QTimer确保在主线程中执行GUI更新
        QTimer.singleShot(0, lambda: self._append_text_safe(msg))

    def _append_text_safe(self, msg):
        """线程安全的文本添加方法"""
        self.text_widget.append(msg)
        # 自动滚动到底部
        self.text_widget.moveCursor(QTextCursor.End)
        self.text_widget.ensureCursorVisible()

class ThreadSafeStdout:
    """线程安全的stdout重定向类"""
    def __init__(self, signal):
        self.signal = signal

    def write(self, text):
        if text.strip():
            self.signal.emit(text.strip())

    def flush(self):
        pass

class ProcessingThread(QThread):
    """处理线程，用于在后台执行DBF处理任务"""
    progress_updated = pyqtSignal(int, str)  # 进度值, 状态信息
    log_message = pyqtSignal(str)  # 日志消息
    finished_signal = pyqtSignal(bool, str)  # 是否成功, 结果信息

    def __init__(self, merger, mode='full'):
        super().__init__()
        self.merger = merger
        self.mode = mode  # 'full' 或 'analysis_only'
        # 重定向print输出到信号
        self.original_stdout = sys.stdout

    def run(self):
        try:
            # 重定向print到信号
            sys.stdout = ThreadSafeStdout(self.log_message)

            if self.mode == 'full':
                # 完整处理流程
                self.progress_updated.emit(5, "开始预处理...")
                self.log_message.emit("🚀 开始完整处理流程...")

                self.merger.preprocess()
                self.progress_updated.emit(15, "预处理完成，开始处理文件...")

                self.merger.process_files()
                self.progress_updated.emit(35, "文件处理完成，开始同源数据分析...")

                # 确保智能识别同源数据分析运行
                if self.merger.processed > 0 and os.path.exists(self.merger.output_path):
                    self.log_message.emit("🔍 开始执行智能识别同源数据分析...")
                    try:
                        analyzer = SameSourceDataAnalyzer(self.merger.output_path)
                        analyzer.run_analysis()
                        self.progress_updated.emit(60, "同源数据分析完成")
                        self.log_message.emit("✅ 智能识别同源数据分析完成！")

                        # 执行风险排查
                        self.progress_updated.emit(65, "开始风险排查...")
                        self.log_message.emit("🔍 开始执行风险排查...")

                        # 仅执行风险信息识别（完整模式不包含IP归属地查询）
                        def risk_progress_callback(value, message):
                            adjusted_value = 65 + int(value * 0.3)
                            self.progress_updated.emit(adjusted_value, message)

                        risk_analyzer = RiskAnalyzer(
                            self.merger.output_path,
                            progress_callback=risk_progress_callback,
                            log_callback=self.log_message.emit
                        )

                        risk_analyzer.run_analysis()
                        self.log_message.emit("✅ 风险信息识别完成！")

                    except Exception as analysis_error:
                        self.log_message.emit(f"⚠️ 同源数据分析出错: {str(analysis_error)}")
                        # 不因为分析错误而终止整个流程
                else:
                    self.log_message.emit("⚠️ 跳过同源数据分析：没有处理的数据或输出文件不存在")

                self.progress_updated.emit(100, "处理完成")
                self.finished_signal.emit(True, f"处理完成！输出文件：{self.merger.output_path}")

            elif self.mode == 'analysis_only':
                # 仅分析模式
                self.progress_updated.emit(20, "开始同源数据分析...")
                self.log_message.emit("🔍 开始智能识别同源数据分析...")

                analyzer = SameSourceDataAnalyzer(self.merger.analysis_file_path)
                analyzer.run_analysis()

                self.progress_updated.emit(100, "分析完成")
                self.finished_signal.emit(True, "分析完成！")

            elif self.mode == 'risk_screening':
                # 风险排查模式
                self.progress_updated.emit(10, "开始风险排查...")
                self.log_message.emit("🔍 开始风险排查模式...")

                # 获取选择的文件
                ip_files = getattr(self.merger, 'ip_source_file_paths', [])
                risk_file = getattr(self.merger, 'risk_analysis_file_path', None)

                total_tasks = len(ip_files) + (1 if risk_file else 0)
                completed_tasks = 0

                # 处理IP归属地查询（每个文件单独处理）
                if ip_files:
                    self.log_message.emit(f"📋 开始处理 {len(ip_files)} 个IP同源数据文件...")

                    for i, ip_file in enumerate(ip_files):
                        self.log_message.emit(f"🌐 处理第 {i+1}/{len(ip_files)} 个IP文件: {os.path.basename(ip_file)}")

                        def ip_progress_callback(value, message):
                            # 计算当前任务的进度范围
                            task_start = 10 + (completed_tasks * 80 // total_tasks)
                            task_range = 80 // total_tasks
                            adjusted_value = task_start + int(value * task_range / 100)
                            self.progress_updated.emit(adjusted_value, f"IP查询({i+1}/{len(ip_files)}): {message}")

                        # 获取缓存设置
                        use_cache = getattr(self.merger, 'use_ip_cache', True)

                        ip_querier = IPLocationQuerier(
                            ip_file,
                            progress_callback=ip_progress_callback,
                            log_callback=self.log_message.emit,
                            use_cache=use_cache
                        )

                        try:
                            result_file = ip_querier.run_query()
                            if result_file:
                                self.log_message.emit(f"✅ IP文件处理完成: {os.path.basename(result_file)}")
                            else:
                                self.log_message.emit(f"⚠️ IP文件未生成结果: {os.path.basename(ip_file)}")
                        except Exception as e:
                            self.log_message.emit(f"❌ IP文件处理失败: {os.path.basename(ip_file)} - {str(e)}")

                        completed_tasks += 1

                # 处理风险信息识别
                if risk_file:
                    self.log_message.emit(f"⚠️ 开始风险信息识别: {os.path.basename(risk_file)}")

                    def risk_progress_callback(value, message):
                        task_start = 10 + (completed_tasks * 80 // total_tasks)
                        task_range = 80 // total_tasks
                        adjusted_value = task_start + int(value * task_range / 100)
                        self.progress_updated.emit(adjusted_value, f"风险识别: {message}")

                    risk_analyzer = RiskAnalyzer(
                        risk_file,
                        progress_callback=risk_progress_callback,
                        log_callback=self.log_message.emit
                    )

                    try:
                        result_file = risk_analyzer.run_analysis()
                        if result_file:
                            self.log_message.emit(f"✅ 风险分析完成: {os.path.basename(result_file)}")
                        else:
                            self.log_message.emit("⚠️ 未发现风险记录")
                    except Exception as e:
                        self.log_message.emit(f"❌ 风险分析失败: {str(e)}")

                self.progress_updated.emit(100, "风险排查完成")
                self.finished_signal.emit(True, "风险排查完成！")

        except Exception as e:
            self.log_message.emit(f"❌ 处理失败：{str(e)}")
            self.finished_signal.emit(False, f"处理失败：{str(e)}")
        finally:
            # 恢复原始stdout
            sys.stdout = self.original_stdout

class DBFMergerGUI(QMainWindow):
    """PyQt5主界面"""

    def __init__(self):
        super().__init__()
        self.merger = DBFMergerPro()
        self.processing_thread = None
        self.init_ui()
        self.setup_logging()
        self.setup_icon()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("同源数据智能识别工具")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)

        # 设置应用程序样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #e1e1e1;
                border: 1px solid #c0c0c0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 10px 20px;
                text-align: center;
                font-size: 14px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QRadioButton {
                font-size: 14px;
                padding: 5px;
            }
            QLabel {
                font-size: 12px;
                color: #333333;
            }
            QProgressBar {
                border: 2px solid #cccccc;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 创建选项卡
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)

        # 创建处理模式选择标签页
        mode_tab = self.create_mode_selection_tab()
        tab_widget.addTab(mode_tab, "🔧 处理模式")

        # 创建日志显示标签页
        log_tab = self.create_log_tab()
        tab_widget.addTab(log_tab, "📋 处理日志")

        # 创建进度显示区域
        progress_group = QGroupBox("📊 处理进度")
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(10)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMinimumHeight(25)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("🟢 就绪")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2e7d32;")
        progress_layout.addWidget(self.status_label)

        main_layout.addWidget(progress_group)

    def create_mode_selection_tab(self):
        """创建处理模式选择标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 模式选择组
        mode_group = QGroupBox("🎯 选择处理模式")
        mode_layout = QVBoxLayout(mode_group)
        mode_layout.setSpacing(15)

        self.mode_button_group = QButtonGroup()

        # 完整处理模式
        full_mode_container = QWidget()
        full_mode_layout = QVBoxLayout(full_mode_container)
        full_mode_layout.setContentsMargins(10, 10, 10, 10)
        full_mode_container.setStyleSheet("""
            QWidget {
                background-color: #e8f5e8;
                border-radius: 8px;
            }
        """)

        self.full_mode_radio = QRadioButton("🔄 完整处理模式")
        self.full_mode_radio.setChecked(True)
        self.full_mode_radio.setStyleSheet("font-size: 16px; font-weight: bold; color: #2e7d32;")
        full_mode_layout.addWidget(self.full_mode_radio)

        full_mode_desc = QLabel("执行文件合并、站点解析、同源数据智能识别和风险排查的完整流程")
        full_mode_desc.setStyleSheet("font-size: 12px; color: #555555; margin-left: 25px;")
        full_mode_desc.setWordWrap(True)
        full_mode_layout.addWidget(full_mode_desc)

        mode_layout.addWidget(full_mode_container)
        self.mode_button_group.addButton(self.full_mode_radio, 0)

        # 跳转分析模式
        analysis_mode_container = QWidget()
        analysis_mode_layout = QVBoxLayout(analysis_mode_container)
        analysis_mode_layout.setContentsMargins(10, 10, 10, 10)
        analysis_mode_container.setStyleSheet("""
            QWidget {
                background-color: #fff3e0;
                border-radius: 8px;
            }
        """)

        self.analysis_mode_radio = QRadioButton("⚡ 跳转分析模式")
        self.analysis_mode_radio.setStyleSheet("font-size: 16px; font-weight: bold; color: #f57c00;")
        analysis_mode_layout.addWidget(self.analysis_mode_radio)

        analysis_mode_desc = QLabel("跳过合并步骤，直接对已处理的文件进行同源数据智能识别")
        analysis_mode_desc.setStyleSheet("font-size: 12px; color: #555555; margin-left: 25px;")
        analysis_mode_desc.setWordWrap(True)
        analysis_mode_layout.addWidget(analysis_mode_desc)

        mode_layout.addWidget(analysis_mode_container)
        self.mode_button_group.addButton(self.analysis_mode_radio, 1)

        # 风险排查模式
        risk_mode_container = QWidget()
        risk_mode_layout = QVBoxLayout(risk_mode_container)
        risk_mode_layout.setContentsMargins(10, 10, 10, 10)
        risk_mode_container.setStyleSheet("""
            QWidget {
                background-color: #fce4ec;
                border-radius: 8px;
            }
        """)

        self.risk_mode_radio = QRadioButton("🔍 风险排查模式")
        self.risk_mode_radio.setStyleSheet("font-size: 16px; font-weight: bold; color: #c2185b;")
        risk_mode_layout.addWidget(self.risk_mode_radio)

        risk_mode_desc = QLabel("基于同源数据智能识别结果进行买户、场外配资等风险排查")
        risk_mode_desc.setStyleSheet("font-size: 12px; color: #555555; margin-left: 25px;")
        risk_mode_desc.setWordWrap(True)
        risk_mode_layout.addWidget(risk_mode_desc)

        mode_layout.addWidget(risk_mode_container)
        self.mode_button_group.addButton(self.risk_mode_radio, 2)

        layout.addWidget(mode_group)

        # 文件选择组
        file_group = QGroupBox("📁 文件选择")
        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(15)

        # 完整模式文件选择
        self.full_mode_widget = QWidget()
        full_layout = QVBoxLayout(self.full_mode_widget)
        full_layout.setSpacing(10)

        dbf_button = QPushButton("📂 选择DBF文件")
        dbf_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                font-size: 14px;
                padding: 12px 24px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        dbf_button.clicked.connect(self.select_dbf_files)
        full_layout.addWidget(dbf_button)

        self.dbf_files_label = QLabel("📄 未选择文件")
        self.dbf_files_label.setStyleSheet("""
            QLabel {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                padding: 8px;
                border-radius: 4px;
                color: #666;
            }
        """)
        full_layout.addWidget(self.dbf_files_label)

        output_button = QPushButton("💾 选择输出路径")
        output_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                font-size: 14px;
                padding: 12px 24px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        output_button.clicked.connect(self.select_output_path)
        full_layout.addWidget(output_button)

        self.output_path_label = QLabel("📁 未选择路径")
        self.output_path_label.setStyleSheet("""
            QLabel {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                padding: 8px;
                border-radius: 4px;
                color: #666;
            }
        """)
        full_layout.addWidget(self.output_path_label)

        file_layout.addWidget(self.full_mode_widget)

        # 分析模式文件选择
        self.analysis_mode_widget = QWidget()
        analysis_layout = QVBoxLayout(self.analysis_mode_widget)
        analysis_layout.setSpacing(10)

        analysis_button = QPushButton("📦 选择DBF合并文件")  # 更换图标和文本
        analysis_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                font-size: 14px;
                padding: 12px 24px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        analysis_button.clicked.connect(self.select_analysis_file)
        analysis_layout.addWidget(analysis_button)

        self.analysis_file_label = QLabel("📦 未选择DBF合并文件")
        self.analysis_file_label.setStyleSheet("""
            QLabel {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                padding: 8px;
                border-radius: 4px;
                color: #666;
            }
        """)
        analysis_layout.addWidget(self.analysis_file_label)

        self.analysis_mode_widget.setVisible(False)
        file_layout.addWidget(self.analysis_mode_widget)

        # 风险排查模式文件选择
        self.risk_mode_widget = QWidget()
        risk_layout = QVBoxLayout(self.risk_mode_widget)
        risk_layout.setSpacing(15)
        risk_layout.setContentsMargins(0, 0, 0, 0)

        # IP归属地查询文件选择区域
        ip_location_button = QPushButton("🌐 选择IP同源数据文件")
        ip_location_button.setStyleSheet("""
            QPushButton {
                background-color: #E91E63;
                font-size: 14px;
                padding: 12px 24px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #C2185B;
            }
        """)
        ip_location_button.clicked.connect(self.select_ip_source_file)
        risk_layout.addWidget(ip_location_button)

        self.ip_source_file_label = QLabel("🌐 未选择IP同源数据文件")
        self.ip_source_file_label.setStyleSheet("""
            QLabel {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                padding: 8px;
                border-radius: 4px;
                color: #666;
            }
        """)
        risk_layout.addWidget(self.ip_source_file_label)

        # 缓存设置区域 - 与文件选择按钮对齐
        from PyQt5.QtWidgets import QCheckBox, QHBoxLayout
        from PyQt5.QtCore import Qt

        cache_settings_container = QWidget()
        cache_settings_container.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        cache_container_layout = QHBoxLayout(cache_settings_container)
        cache_container_layout.setSpacing(15)
        cache_container_layout.setContentsMargins(15, 15, 15, 15)

        # 缓存标题
        cache_title = QLabel("🗄️ 缓存设置")
        cache_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #333;")
        cache_container_layout.addWidget(cache_title)

        # 缓存开关复选框 - 使用自定义显示方式
        # 创建自定义复选框容器
        checkbox_container = QWidget()
        checkbox_layout = QHBoxLayout(checkbox_container)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)
        checkbox_layout.setSpacing(8)

        # 状态指示器标签
        self.cache_status_indicator = QLabel("✓")
        self.cache_status_indicator.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                font-size: 16px;
                font-weight: bold;
                border: 2px solid #4CAF50;
                background-color: white;
                border-radius: 3px;
                width: 18px;
                height: 18px;
                text-align: center;
                padding: 0px;
                margin: 0px;
            }
        """)
        self.cache_status_indicator.setFixedSize(22, 22)
        self.cache_status_indicator.setAlignment(Qt.AlignCenter)

        # 文本标签
        cache_text_label = QLabel("选择使用IP地址缓存")
        cache_text_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #333;
                font-weight: 500;
                padding: 2px;
            }
        """)

        # 隐藏的复选框用于状态管理
        self.use_cache_checkbox = QCheckBox()
        self.use_cache_checkbox.setChecked(True)  # 默认启用缓存
        self.use_cache_checkbox.setVisible(False)  # 隐藏原生复选框

        # 组装布局
        checkbox_layout.addWidget(self.cache_status_indicator)
        checkbox_layout.addWidget(cache_text_label)
        checkbox_layout.addStretch()

        # 点击事件处理
        def toggle_cache_state():
            current_state = self.use_cache_checkbox.isChecked()
            new_state = not current_state
            self.use_cache_checkbox.setChecked(new_state)
            self.update_cache_indicator(new_state)
            self.on_cache_setting_changed()

        # 为指示器和文本添加点击事件
        self.cache_status_indicator.mousePressEvent = lambda _: toggle_cache_state()
        cache_text_label.mousePressEvent = lambda _: toggle_cache_state()

        # 初始化指示器状态
        self.update_cache_indicator(True)
        cache_container_layout.addWidget(checkbox_container)

        # 添加弹性空间推动内容到左侧
        cache_container_layout.addStretch()

        risk_layout.addWidget(cache_settings_container)

        # 风险信息识别文件选择
        risk_analysis_button = QPushButton("📦 选择DBF合并文件")
        risk_analysis_button.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                font-size: 14px;
                padding: 12px 24px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #E64A19;
            }
        """)
        risk_analysis_button.clicked.connect(self.select_risk_analysis_file)
        risk_layout.addWidget(risk_analysis_button)

        self.risk_analysis_file_label = QLabel("📦 未选择DBF合并文件")
        self.risk_analysis_file_label.setStyleSheet("""
            QLabel {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                padding: 8px;
                border-radius: 4px;
                color: #666;
            }
        """)
        risk_layout.addWidget(self.risk_analysis_file_label)

        self.risk_mode_widget.setVisible(False)
        file_layout.addWidget(self.risk_mode_widget)

        layout.addWidget(file_group)

        # 控制按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        self.start_button = QPushButton("🚀 开始处理")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 30px;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.start_button.clicked.connect(self.start_processing)
        self.start_button.setEnabled(False)
        button_layout.addWidget(self.start_button)

        self.stop_button = QPushButton("⏹️ 停止处理")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 30px;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)

        # 添加弹性空间
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 连接信号
        self.mode_button_group.buttonClicked.connect(self.on_mode_changed)
        self.use_cache_checkbox.stateChanged.connect(self.on_cache_setting_changed)

        return tab

    def create_log_tab(self):
        """创建日志显示标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)

        # 日志显示区域
        log_group = QGroupBox("📋 处理日志")
        log_group_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 10))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 2px solid #333333;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                line-height: 1.4;
            }
        """)
        # 设置初始欢迎消息
        self.log_text.append("🎉 欢迎使用同源数据智能识别工具")
        self.log_text.append("📝 处理日志将在此显示...")
        self.log_text.append("=" * 60)

        log_group_layout.addWidget(self.log_text)
        layout.addWidget(log_group)

        # 日志控制按钮
        log_button_layout = QHBoxLayout()
        log_button_layout.setSpacing(10)

        clear_button = QPushButton("🗑️ 清空日志")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #ff5722;
                font-size: 14px;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e64a19;
            }
        """)
        clear_button.clicked.connect(self.clear_log)
        log_button_layout.addWidget(clear_button)

        save_log_button = QPushButton("💾 保存日志")
        save_log_button.setStyleSheet("""
            QPushButton {
                background-color: #607d8b;
                font-size: 14px;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #546e7a;
            }
        """)
        save_log_button.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_log_button)

        # 添加弹性空间
        log_button_layout.addStretch()

        layout.addLayout(log_button_layout)

        return tab

    def setup_logging(self):
        """设置日志系统"""
        # 创建自定义日志处理器
        self.gui_handler = GuiLogHandler(self.log_text)
        self.gui_handler.setLevel(logging.INFO)

        # 设置日志格式
        formatter = logging.Formatter('%(asctime)s - %(message)s', datefmt='%H:%M:%S')
        self.gui_handler.setFormatter(formatter)

        # 获取根日志器并添加处理器
        logger = logging.getLogger()
        logger.addHandler(self.gui_handler)
        logger.setLevel(logging.INFO)

        # 重定向print输出到日志
        sys.stdout = LogRedirector(self.log_text)

    def setup_icon(self):
        """设置应用程序图标"""
        try:
            if BASE64_ICON_DATA and BASE64_ICON_DATA.strip():
                # 解码base64图标数据
                icon_data = base64.b64decode(BASE64_ICON_DATA)

                # 创建QPixmap和QIcon
                pixmap = QPixmap()
                if pixmap.loadFromData(icon_data):
                    icon = QIcon(pixmap)

                    # 设置窗口图标
                    self.setWindowIcon(icon)

                    # 设置应用程序图标（任务栏显示）
                    QApplication.instance().setWindowIcon(icon)

                    print("✅ 应用程序图标设置成功")
                else:
                    print("⚠️ 图标数据格式无效，使用默认图标")
            else:
                print("ℹ️ 未提供图标数据，使用默认图标")
                print("💡 提示：可在代码中的BASE64_ICON_DATA变量中添加base64编码的图标数据")

        except Exception as e:
            print(f"⚠️ 设置图标时出错: {str(e)}，使用默认图标")

    def on_mode_changed(self):
        """处理模式改变事件"""
        if self.full_mode_radio.isChecked():
            self.full_mode_widget.setVisible(True)
            self.analysis_mode_widget.setVisible(False)
            self.risk_mode_widget.setVisible(False)
        elif self.analysis_mode_radio.isChecked():
            self.full_mode_widget.setVisible(False)
            self.analysis_mode_widget.setVisible(True)
            self.risk_mode_widget.setVisible(False)
        else:  # risk_mode_radio.isChecked()
            self.full_mode_widget.setVisible(False)
            self.analysis_mode_widget.setVisible(False)
            self.risk_mode_widget.setVisible(True)
        self.update_start_button_state()

    def update_cache_indicator(self, is_checked):
        """更新缓存指示器的视觉状态"""
        if is_checked:
            # 选中状态：显示绿色勾选
            self.cache_status_indicator.setText("✓")
            self.cache_status_indicator.setStyleSheet("""
                QLabel {
                    color: #4CAF50;
                    font-size: 16px;
                    font-weight: bold;
                    border: 2px solid #4CAF50;
                    background-color: white;
                    border-radius: 3px;
                    width: 18px;
                    height: 18px;
                    text-align: center;
                    padding: 0px;
                    margin: 0px;
                }
            """)
        else:
            # 未选中状态：显示灰色空框
            self.cache_status_indicator.setText("")
            self.cache_status_indicator.setStyleSheet("""
                QLabel {
                    color: #ccc;
                    font-size: 16px;
                    font-weight: bold;
                    border: 2px solid #ccc;
                    background-color: white;
                    border-radius: 3px;
                    width: 18px;
                    height: 18px;
                    text-align: center;
                    padding: 0px;
                    margin: 0px;
                }
            """)

    def on_cache_setting_changed(self):
        """处理缓存设置改变事件"""
        use_cache = self.use_cache_checkbox.isChecked()
        self.merger.use_ip_cache = use_cache

        # 记录设置变更
        if use_cache:
            self.add_log_message("🗄️ IP地址缓存读取已启用 - 将优先使用已缓存的IP地址")
        else:
            self.add_log_message("🗄️ IP地址缓存读取已禁用 - 将直接查询所有IP地址")
            self.add_log_message("⚠️ 注意：禁用缓存读取将增加API查询次数和处理时间")

        self.add_log_message("💾 提示：缓存写入始终启用，查询结果将实时保存到本地缓存")

    def select_dbf_files(self):
        """选择DBF文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择要合并的DBF文件", "", "DBF Files (*.dbf)"
        )
        if files:
            self.merger.input_files = files
            self.dbf_files_label.setText(f"✅ 已选择 {len(files)} 个文件")
            self.dbf_files_label.setStyleSheet("""
                QLabel {
                    background-color: #e8f5e8;
                    border: 1px solid #4CAF50;
                    padding: 8px;
                    border-radius: 4px;
                    color: #2e7d32;
                    font-weight: bold;
                }
            """)
            self.update_start_button_state()

    def select_output_path(self):
        """选择输出路径"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存合并结果", "",
            "Parquet文件 (*.parquet);;CSV文件 (*.csv)"
        )
        if file_path:
            self.merger.output_path = file_path
            self.output_path_label.setText(f"✅ {os.path.basename(file_path)}")
            self.output_path_label.setStyleSheet("""
                QLabel {
                    background-color: #e8f5e8;
                    border: 1px solid #4CAF50;
                    padding: 8px;
                    border-radius: 4px;
                    color: #2e7d32;
                    font-weight: bold;
                }
            """)
            self.update_start_button_state()

    def select_analysis_file(self):
        """选择要分析的文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择DBF合并文件", "",
            "CSV文件 (*.csv);;Parquet文件 (*.parquet)"
        )
        if file_path:
            self.merger.analysis_file_path = file_path
            self.analysis_file_label.setText(f"✅ {os.path.basename(file_path)}")
            self.analysis_file_label.setStyleSheet("""
                QLabel {
                    background-color: #e8f5e8;
                    border: 1px solid #4CAF50;
                    padding: 8px;
                    border-radius: 4px;
                    color: #2e7d32;
                    font-weight: bold;
                }
            """)
            self.update_start_button_state()

    def select_ip_source_file(self):
        """选择IP同源数据文件（多选）"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择IP同源数据文件（可多选）", "",
            "Excel文件 (*.xlsx);;CSV文件 (*.csv)"
        )
        if file_paths:
            self.merger.ip_source_file_paths = file_paths
            file_count = len(file_paths)
            if file_count == 1:
                self.ip_source_file_label.setText(f"✅ {os.path.basename(file_paths[0])}")
            else:
                self.ip_source_file_label.setText(f"✅ 已选择 {file_count} 个IP同源数据文件")
            self.ip_source_file_label.setStyleSheet("""
                QLabel {
                    background-color: #e8f5e8;
                    border: 1px solid #4CAF50;
                    padding: 8px;
                    border-radius: 4px;
                    color: #2e7d32;
                    font-weight: bold;
                }
            """)
            self.update_start_button_state()

    def select_risk_analysis_file(self):
        """选择风险分析文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择DBF合并文件", "",
            "CSV文件 (*.csv);;Parquet文件 (*.parquet)"
        )
        if file_path:
            self.merger.risk_analysis_file_path = file_path
            self.risk_analysis_file_label.setText(f"✅ {os.path.basename(file_path)}")
            self.risk_analysis_file_label.setStyleSheet("""
                QLabel {
                    background-color: #e8f5e8;
                    border: 1px solid #4CAF50;
                    padding: 8px;
                    border-radius: 4px;
                    color: #2e7d32;
                    font-weight: bold;
                }
            """)
            self.update_start_button_state()



    def update_start_button_state(self):
        """更新开始按钮状态"""
        if self.full_mode_radio.isChecked():
            enabled = bool(self.merger.input_files and self.merger.output_path)
        elif self.analysis_mode_radio.isChecked():
            enabled = hasattr(self.merger, 'analysis_file_path') and bool(self.merger.analysis_file_path)
        else:  # risk_mode_radio.isChecked()
            # 风险排查模式：至少需要选择一个文件（IP文件或风险分析文件）
            has_ip_files = hasattr(self.merger, 'ip_source_file_paths') and bool(self.merger.ip_source_file_paths)
            has_risk_file = hasattr(self.merger, 'risk_analysis_file_path') and bool(self.merger.risk_analysis_file_path)
            enabled = has_ip_files or has_risk_file
        self.start_button.setEnabled(enabled)

    def start_processing(self):
        """开始处理"""
        if self.full_mode_radio.isChecked():
            mode = 'full'
            mode_name = '完整处理'
        elif self.analysis_mode_radio.isChecked():
            mode = 'analysis_only'
            mode_name = '跳转分析'
        else:
            mode = 'risk_screening'
            mode_name = '风险排查'

        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("🔄 处理中...")
        self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #ff9800;")

        # 清空之前的日志并添加开始信息
        self.log_text.append("\n" + "="*60)
        self.log_text.append(f"🚀 开始处理 - 模式: {mode_name}")
        self.log_text.append("="*60)

        # 创建并启动处理线程
        self.processing_thread = ProcessingThread(self.merger, mode)
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.log_message.connect(self.add_log_message)
        self.processing_thread.finished_signal.connect(self.processing_finished)
        self.processing_thread.start()

    def stop_processing(self):
        """停止处理"""
        if self.processing_thread and self.processing_thread.isRunning():
            self.merger.is_running = False
            self.processing_thread.terminate()
            self.processing_thread.wait()
            self.processing_finished(False, "用户中断处理")

    def update_progress(self, value, message):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.status_label.setText(f"🔄 {message}")

    def add_log_message(self, message):
        """添加日志消息"""
        # 添加时间戳
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        # 确保自动滚动到底部
        self.log_text.moveCursor(QTextCursor.End)
        self.log_text.ensureCursorVisible()

    def processing_finished(self, success, message):
        """处理完成"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText("✅ 处理完成")
            self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #4CAF50;")
            self.log_text.append("\n" + "="*60)
            self.log_text.append("✅ 处理成功完成！")
            self.log_text.append("="*60)
            QMessageBox.information(self, "🎉 成功", message)
        else:
            self.status_label.setText("❌ 处理失败")
            self.status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #f44336;")
            self.log_text.append("\n" + "="*60)
            self.log_text.append("❌ 处理失败！")
            self.log_text.append("="*60)
            QMessageBox.warning(self, "⚠️ 错误", message)

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log_text.append("🎉 欢迎使用同源数据智能识别工具")
        self.log_text.append("📝 处理日志将在此显示...")
        self.log_text.append("=" * 60)

    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存日志", "processing_log.txt", "文本文件 (*.txt)"
        )
        if file_path:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.log_text.toPlainText())
            QMessageBox.information(self, "💾 成功", f"日志已保存到：{file_path}")

class LogRedirector:
    """重定向print输出到GUI - 增强版本"""
    def __init__(self, text_widget):
        self.text_widget = text_widget
        self.original_stdout = sys.stdout

    def write(self, text):
        if text.strip():  # 只处理非空文本
            try:
                # 检查widget是否仍然有效
                if hasattr(self.text_widget, 'append'):
                    self.text_widget.append(text.strip())
                else:
                    # 如果widget无效，回退到原始stdout
                    self.original_stdout.write(text)
            except RuntimeError:
                # 如果widget已被删除，回退到原始stdout
                self.original_stdout.write(text)

    def flush(self):
        try:
            if hasattr(self.original_stdout, 'flush'):
                self.original_stdout.flush()
        except:
            pass

class DBFMergerPro:
    class MemoryMonitor:
        """实时内存监控器"""
        def __init__(self):
            self._process = psutil.Process()
            self.total_mem = psutil.virtual_memory().total / (1024**3)  # 总内存(GB)
            
        @property
        def usage(self) -> float:
            """当前进程内存占用(GB)"""
            return self._process.memory_info().rss / (1024**3)
        
        @property
        def safe_limit(self) -> float:
            """计算实际内存警戒线"""
            return MEMORY_SAFE_LIMIT * self.total_mem

    def __init__(self):
        self.input_files = []       # 输入文件列表
        self.output_path = ""       # 输出路径
        self.analysis_file_path = ""  # 分析模式的输入文件路径
        self.active_columns = set() # 需要生成的拆分列
        self.total_records = 0      # 总记录数
        self.processed = 0          # 已处理记录数
        self.start_time = 0         # 处理开始时间
        self.mem_monitor = self.MemoryMonitor()
        self.is_running = True      # 运行状态标志位

    # ----------------- 核心处理流程 -----------------
    def preprocess(self):
        """
        预处理：扫描所有文件结构并收集拆分元素
        实现两阶段扫描策略确保字段映射准确性
        """
        print("\n🔍 开始预处理扫描...")
        column_freq = defaultdict(int)
        total_files = len(self.input_files)
        all_field_patterns = set()  # 收集所有唯一的字段模式

        # 第一轮扫描：全面分析字段结构（扫描所有记录，不限制1000行）
        print("第一阶段：全面扫描所有站点地址字段模式...")
        with tqdm(self.input_files, desc="扫描字段模式") as pbar:
            for idx, file in enumerate(pbar, 1):
                try:
                    dbf = self._safe_open_dbf(file)
                    record_count = 0
                    for record in dbf:
                        record_count += 1
                        if value := record.get(SPLIT_FIELD, ""):
                            # 使用新的解析逻辑
                            parsed_fields = self._enhanced_recursive_split(value)
                            for field_name in parsed_fields.keys():
                                if field_name != "其他站点信息":  # 排除其他信息字段
                                    column_freq[field_name] += 1
                                    all_field_patterns.add(field_name)

                    pbar.set_postfix_str(f"{os.path.basename(file)} [文件{idx}/{total_files}] 记录:{record_count}")
                except Exception as e:
                    print(f"\n⚠️ 跳过问题文件 {os.path.basename(file)} 原因: {str(e)}")
                    self.input_files.remove(file)

        # 创建完整的字段列表（包括低频字段以确保完整性）
        self.active_columns = all_field_patterns.copy()

        # 确保"其他站点信息"字段始终存在且位于最后
        if "其他站点信息" in self.active_columns:
            self.active_columns.remove("其他站点信息")

        print(f"✅ 第一阶段完成：识别到 {len(self.active_columns)} 个唯一字段模式")
        print(f"📋 字段列表: {sorted(self.active_columns)}")

        # 第二轮扫描：计算总行数
        print("\n📊 正在计算总数据量...")
        with tqdm(self.input_files, desc="统计记录数") as pbar:
            self.total_records = sum(
                sum(1 for _ in self._safe_open_dbf(file)) 
                for file in pbar
            )
        print(f"📂 总计需要处理 {self.total_records} 条记录")

    def process_files(self):
        """主处理流程"""
        self.start_time = time.time()
        chunk_size = INITIAL_CHUNK_SIZE  # 动态分块大小
        
        try:
            with tqdm(total=self.total_records, desc="总进度", unit='rec') as main_bar:
                for file in self.input_files:
                    if not self.is_running: break
                    
                    # 单个文件处理流程
                    file_name = os.path.basename(file)
                    dbf = self._safe_open_dbf(file)
                    file_records = sum(1 for _ in dbf)
                    dbf = self._safe_open_dbf(file)  # 重新打开
                    
                    # 文件级进度条
                    with tqdm(total=file_records, desc=f"处理 {file_name}", leave=False) as file_bar:
                        chunk = []
                        for record in dbf:
                            if not self.is_running: break
                            
                            # 处理单条记录
                            processed = self._process_record(record)
                            chunk.append(processed)
                            
                            # 分块写入
                            if len(chunk) >= chunk_size:
                                self._write_chunk(chunk, main_bar, file_bar)
                                chunk_size = self._adjust_chunk(chunk_size)
                                chunk = []
                                self._print_status(file_name)
                                
                        # 写入剩余数据
                        if chunk and self.is_running:
                            self._write_chunk(chunk, main_bar, file_bar)
                            self._print_status(file_name)
        except Exception as e:
            print(f"\n❌ 处理异常终止: {str(e)}")
        finally:
            if self.processed > 0:
                print(f"\n💾 已成功处理 {self.processed} 条记录")
            else:
                print("\n⛔ 未处理任何有效数据")

    # ----------------- 核心工具方法 -----------------
    def _enhanced_recursive_split(self, value: str) -> Dict[str, str]:
        """
        增强版站点地址解析，支持分号(;)和管道(|)两种分隔符格式

        支持格式：
        1. 分号格式: "MA;IIP=*******;MAC=ABCDEF"
        2. 管道格式: "MA|IIP=*******|MAC=ABCDEF|IMSI=NA|NA|mobile"

        新增功能：
        1. 11位纯数字预检查
        2. 双格式自动检测
        3. 严格的字段名映射
        4. 科学计数法防护
        5. 特殊模式处理（IMSI=NA|NA|mobile）
        """
        parsed_fields = {}
        other_info_parts = []

        try:
            # 1. 判断站点地址是否为空
            if not value or str(value).strip() == "":
                return parsed_fields

            value_str = str(value).strip()

            # 2. 11位纯数字预检查
            if self._is_11_digit_only(value_str):
                parsed_fields["其他站点信息"] = value_str
                return parsed_fields

            # 3. 检测分隔符格式并解析
            format_type, parts = self._detect_format_and_split(value_str)

            # 4. 处理设备类型（仅对分号和管道格式）
            if format_type in ['semicolon', 'pipe'] and parts:
                first_part = parts[0].strip()
                if first_part:
                    if '=' not in first_part and ':' not in first_part:
                        parsed_fields["设备类型"] = self._format_as_string(first_part)
                        parts = parts[1:]

            # 5. 处理键值对部分 - 根据格式类型选择解析方法
            if format_type == 'pipe':
                # 管道格式需要特殊处理
                self._parse_pipe_format_parts(parts, parsed_fields, other_info_parts)
            elif format_type == 'comma_colon':
                # 逗号冒号格式需要特殊处理
                self._parse_comma_colon_format_parts(parts, parsed_fields, other_info_parts)
            else:
                # 分号格式使用原有逻辑
                self._parse_semicolon_format_parts(parts, parsed_fields, other_info_parts)

            # 6. 处理其他未被提取的信息
            if other_info_parts:
                separator_char = '|' if format_type == 'pipe' else (',' if format_type == 'comma_colon' else ';')
                parsed_fields["其他站点信息"] = separator_char.join(other_info_parts)

        except UnicodeDecodeError:
            try:
                decoded = value.encode('raw_unicode_escape').decode('gb18030', errors='replace')
                return self._enhanced_recursive_split(decoded)
            except:
                parsed_fields["其他站点信息"] = str(value)
        except Exception as e:
            print(f"⚠️ 解析站点地址时出错: {str(e)}, 原始值: {value}")
            parsed_fields["其他站点信息"] = str(value)

        return parsed_fields

    def _detect_format_and_split(self, value_str: str) -> tuple:
        """
        检测分隔符格式并分割字符串
        支持三种格式：
        1. 分号格式: "MA;IIP=*******;MAC=ABCDEF"
        2. 管道格式: "MA|IIP=*******|MAC=ABCDEF"
        3. 逗号冒号格式: "MAC:02:00:00:00:00:00,UDID:BAAFE101-2JM5-0B5T-04CV-VFDGVFD1S"

        返回: (format_type, parts)
        """
        # 检测逗号冒号格式
        if ',' in value_str and ':' in value_str:
            # 逗号冒号格式的特征：包含逗号和冒号
            # 进一步验证：检查是否符合 field:value,field:value 模式
            if self._is_comma_colon_format(value_str):
                return 'comma_colon', value_str.split(',')

        # 检测管道格式
        if '|' in value_str and ('=' in value_str or value_str.count('|') > 1):
            # 管道格式的特征：包含|且包含=或多个|
            return 'pipe', value_str.split('|')

        # 默认为分号格式
        return 'semicolon', value_str.split(';')

    def _is_comma_colon_format(self, value_str: str) -> bool:
        """
        验证是否为逗号冒号格式
        检查每个逗号分隔的部分是否包含冒号
        特殊处理：HDInfo= 前缀
        """
        # 处理 HDInfo= 前缀的情况
        if '=' in value_str and ',' in value_str:
            # 检查是否为 HDInfo=field:value,field:value 格式
            equal_index = value_str.find('=')
            prefix = value_str[:equal_index].strip()
            content = value_str[equal_index + 1:].strip()

            # 如果前缀看起来像字段名且内容包含逗号冒号模式
            if prefix and ',' in content and ':' in content:
                return self._validate_comma_colon_content(content)

        # 标准逗号冒号格式验证
        return self._validate_comma_colon_content(value_str)

    def _validate_comma_colon_content(self, content: str) -> bool:
        """验证内容是否符合逗号冒号格式"""
        parts = content.split(',')
        if len(parts) < 2:  # 至少要有2个部分才算逗号分隔格式
            return False

        # 检查每个部分是否包含冒号
        for part in parts:
            part = part.strip()
            if ':' not in part:
                return False

        return True

    def _parse_pipe_format_parts(self, parts: list, parsed_fields: dict, other_info_parts: list):
        """
        解析管道格式的部分
        特殊处理: IMSI=NA|NA|mobile 模式
        """
        i = 0
        while i < len(parts):
            part = parts[i].strip()
            if not part:
                i += 1
                continue

            if '=' in part:
                key, val = part.split('=', 1)
                key = key.strip()
                val = val.strip()

                if key and val:
                    # 检查是否为特殊的IMSI模式
                    if key == 'IMSI' and i + 2 < len(parts):
                        # 检查后续两个部分是否为 "NA" 和 "mobile"
                        next_part1 = parts[i + 1].strip() if i + 1 < len(parts) else ""
                        next_part2 = parts[i + 2].strip() if i + 2 < len(parts) else ""

                        if next_part1 == 'NA' and next_part2 == 'mobile':
                            # 合并为完整的IMSI值
                            combined_value = f"{val}|{next_part1}|{next_part2}"
                            parsed_fields[key] = self._format_as_string(combined_value)
                            i += 3  # 跳过已处理的三个部分
                            continue

                    # 普通键值对处理
                    parsed_fields[key] = self._format_as_string(val)
                else:
                    other_info_parts.append(part)
            else:
                other_info_parts.append(part)

            i += 1

    def _parse_comma_colon_format_parts(self, parts: list, parsed_fields: dict, other_info_parts: list):
        """
        解析逗号冒号格式的部分
        格式: "field:value,field:value" 或 "HDInfo=field:value,field:value"
        特殊处理: 只在第一个冒号处分割，保护MAC地址和IPv6地址
        """
        for i, part in enumerate(parts):
            part = part.strip()
            if not part:
                continue

            # 处理第一个部分可能包含前缀的情况（如 HDInfo=MAC:value）
            if i == 0 and '=' in part and ':' in part:
                # 检查是否为 prefix=field:value 格式
                equal_index = part.find(':')
                before_colon = part[:equal_index]

                if '=' in before_colon:
                    # 这是 HDInfo=MAC:value 格式，提取前缀
                    prefix_equal_index = before_colon.find('=')
                    prefix = before_colon[:prefix_equal_index].strip()
                    field = before_colon[prefix_equal_index + 1:].strip()
                    value = part[equal_index + 1:].strip()

                    # 将前缀放入其他信息
                    if prefix:
                        other_info_parts.append(prefix)

                    # 解析字段值对
                    if field and value:
                        parsed_fields[field] = self._format_as_string(value)
                    continue

            # 标准的 field:value 处理
            if ':' in part:
                # 只在第一个冒号处分割，保护复杂值（如MAC地址、IPv6地址）
                colon_index = part.find(':')
                key = part[:colon_index].strip()
                val = part[colon_index + 1:].strip()

                if key and val:
                    parsed_fields[key] = self._format_as_string(val)
                else:
                    other_info_parts.append(part)
            else:
                other_info_parts.append(part)

    def _parse_semicolon_format_parts(self, parts: list, parsed_fields: dict, other_info_parts: list):
        """
        解析分号格式的部分（原有逻辑）
        """
        for part in parts:
            part = part.strip()
            if not part:
                continue

            if '=' in part:
                key, val = part.split('=', 1)
                key = key.strip()
                val = val.strip()

                if key and val:
                    parsed_fields[key] = self._format_as_string(val)
                else:
                    other_info_parts.append(part)
            else:
                other_info_parts.append(part)

    def _recursive_split(self, value: str) -> Dict[str, str]:
        """保持向后兼容的原始方法，内部调用增强版本"""
        return self._enhanced_recursive_split(value)

    def _is_11_digit_only(self, value: str) -> bool:
        """检查是否为11位纯数字"""
        return len(value) == 11 and value.isdigit()

    def _format_as_string(self, value: str) -> str:
        """
        强制格式化为字符串，防止科学计数法
        特别处理长数字和特殊格式 - 增强版本
        """
        if not value:
            return ""

        value_str = str(value).strip()

        # 如果是空字符串，直接返回
        if not value_str:
            return ""

        # 检查是否为纯数字字符串，如果是长数字则需要特别保护
        if value_str.isdigit() and len(value_str) > 10:
            # 对于超过10位的纯数字，强制保持字符串格式
            return value_str

        # 检查是否已经是科学计数法格式，如果是则保持原样
        if 'e' in value_str.lower():
            return value_str

        # 对于可能被转换为科学计数法的数字，进行预防性处理
        if self._is_numeric(value_str):
            try:
                # 检查数字的长度，如果整数部分很长，保持字符串格式
                float_val = float(value_str)
                if abs(float_val) >= 1e10:  # 大于等于100亿的数字
                    return value_str
            except (ValueError, OverflowError):
                pass

        # 确保返回字符串格式，保持原始格式
        return value_str

    def _safe_open_dbf(self, file_path: str) -> DBF:
        """安全打开DBF文件（自动检测编码）"""
        for encoding in ENCODING_CANDIDATES:
            try:
                dbf = DBF(
                    file_path,
                    encoding=encoding,
                    ignore_missing_memofile=True,
                    char_decode_errors='replace'
                )
                if SPLIT_FIELD in dbf.field_names:
                    return dbf
            except (UnicodeDecodeError, DBFNotFound) as e:
                continue
        raise ValueError(f"无法解码文件 {os.path.basename(file_path)}")

    def _process_record(self, record: dict) -> dict:
        """
        处理单条记录 - 增强版本
        1. 保留原始站点地址字段
        2. 处理金额字段的小数精度
        3. 确保字段顺序正确
        """
        # 保留所有原始字段（包括站点地址字段）
        row = dict(record)

        # 处理金额字段的小数精度
        self._format_amount_fields(row)

        # 处理站点地址拆分字段
        if value := record.get(SPLIT_FIELD, ""):
            parsed_fields = self._enhanced_recursive_split(value)

            # 按字段名严格映射到对应列
            for field_name, field_value in parsed_fields.items():
                if field_name in self.active_columns or field_name == "其他站点信息":
                    # 确保所有值都是字符串格式
                    row[field_name] = self._format_as_string(field_value)

        # 确保"其他站点信息"字段存在（即使为空）
        if "其他站点信息" not in row:
            row["其他站点信息"] = ""

        return row

    def _format_amount_fields(self, row: dict):
        """
        格式化金额字段，确保保持2位小数精度
        """
        amount_fields = ["发生金额", "后资金额"]  # 可以根据实际字段名调整

        for field in amount_fields:
            if field in row and row[field] is not None:
                try:
                    # 转换为浮点数然后格式化为2位小数
                    amount_value = float(row[field])
                    row[field] = f"{amount_value:.2f}"
                except (ValueError, TypeError):
                    # 如果转换失败，保持原值
                    pass

    def _write_chunk(self, chunk: list, main_bar: tqdm, file_bar: tqdm):
        """
        安全写入数据块 - 增强版本
        1. 确保列顺序正确
        2. 强制字符串格式化防止科学计数法
        3. "其他站点信息"字段置于最后
        """
        df = pd.DataFrame(chunk)

        # 补全缺失的拆分字段列
        missing_cols = self.active_columns - set(df.columns)
        for col in missing_cols:
            df[col] = ""

        # 确保"其他站点信息"字段存在
        if "其他站点信息" not in df.columns:
            df["其他站点信息"] = ""

        # 重新排列列顺序：原始字段 + 拆分字段 + "其他站点信息"
        df = self._reorder_columns(df)

        # 强制所有拆分字段为字符串格式，防止科学计数法
        df = self._ensure_string_format(df)

        # 验证数据完整性
        validation_results = self._validate_data_integrity(df)
        if not validation_results['validation_passed']:
            print(f"\n⚠️ 数据完整性警告：检测到潜在的数据损坏")
            for corruption in validation_results['potential_corruption']:
                print(f"   - 字段 '{corruption['field']}': {corruption['high_risk_samples']}")

        # 选择存储格式
        if self.output_path.endswith('.parquet'):
            self._write_parquet(df)
        else:
            self._write_csv(df)

        # 更新进度
        chunk_len = len(chunk)
        main_bar.update(chunk_len)
        file_bar.update(chunk_len)
        self.processed += chunk_len

        # 主动内存回收
        del df, chunk
        gc.collect()

    def _reorder_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        重新排列列顺序：原始字段 + 拆分字段 + "其他站点信息"
        """
        # 获取所有列
        all_columns = list(df.columns)

        # 分离原始字段和拆分字段
        original_fields = [col for col in all_columns if col not in self.active_columns and col != "其他站点信息"]
        parsed_fields = [col for col in all_columns if col in self.active_columns]

        # 构建新的列顺序
        new_order = original_fields + sorted(parsed_fields)

        # "其他站点信息"字段始终放在最后
        if "其他站点信息" in all_columns:
            new_order.append("其他站点信息")

        return df[new_order]

    def _ensure_string_format(self, df: pd.DataFrame):
        """
        确保所有拆分字段都是字符串格式，防止科学计数法
        """
        for col in self.active_columns:
            if col in df.columns:
                # 强制转换为字符串，处理NaN值
                df[col] = df[col].astype(str).replace('nan', '')

        # 特别处理"其他站点信息"字段
        if "其他站点信息" in df.columns:
            df["其他站点信息"] = df["其他站点信息"].astype(str).replace('nan', '')

    def _is_boolean_field(self, field_name: str) -> bool:
        """判断字段是否应该为布尔类型"""
        # 新的解析逻辑中，所有字段都是字符串类型，不需要布尔类型
        # 保留此方法以兼容现有代码结构，但始终返回False
        _ = field_name  # 避免未使用变量警告
        return False

    def _adjust_chunk(self, current_size: int) -> int:
        """动态调整分块大小"""
        if self.mem_monitor.usage > self.mem_monitor.safe_limit:
            new_size = max(500, int(current_size * 0.6))  # 内存超限时缩小分块
            print(f"\n⚠️ 内存告警 ({self.mem_monitor.usage:.1f}GB)，分块调整为 {new_size}")
            return new_size
        return min(current_size * 2, INITIAL_CHUNK_SIZE * 8)  # 安全时扩大分块

    def _print_status(self, filename: str):
        """打印实时状态"""
        elapsed = time.time() - self.start_time
        speed = self.processed / (elapsed + 1e-5)
        status = (
            f"\n【状态更新 {time.strftime('%H:%M:%S')}】"
            f"\n当前文件：{filename}"
            f"\n处理进度：{self.processed:,}/{self.total_records:,} ({self.processed/self.total_records:.1%})"
            f"\n运行时长：{int(elapsed//3600)}h {int(elapsed%3600//60)}m {int(elapsed%60)}s"
            f"\n处理速度：{speed:,.0f} 行/秒"
            f"\n内存使用：{self.mem_monitor.usage:.1f}GB / {self.mem_monitor.total_mem:.1f}GB"
        )
        print(status)

    # ----------------- 数据保存方法 -----------------
    def _write_parquet(self, df: pd.DataFrame):
        """
        写入Parquet格式 - 增强版本
        确保数据完整性，防止科学计数法转换
        """
        # 创建数据副本进行处理
        df_copy = df.copy()

        # 对所有拆分字段强制转换为字符串类型
        for col in self.active_columns:
            if col in df_copy.columns:
                # 强制转换为字符串，保持原始格式
                df_copy[col] = df_copy[col].astype(str).replace('nan', '')

        # 特别处理"其他站点信息"字段
        if "其他站点信息" in df_copy.columns:
            df_copy["其他站点信息"] = df_copy["其他站点信息"].astype(str).replace('nan', '')

        df_copy.to_parquet(
            self.output_path,
            engine='pyarrow',
            compression='zstd',
            index=False,
            existing_data='append' if os.path.exists(self.output_path) else 'overwrite'
        )

    def _write_csv(self, df: pd.DataFrame):
        """
        写入CSV格式（Excel兼容）- 增强版本
        完全防止科学计数法显示问题
        """
        # 创建数据副本进行处理
        df_copy = df.copy()

        # 首先对所有拆分字段进行强化保护
        for col in self.active_columns:
            if col in df_copy.columns:
                # 应用增强的数字保护逻辑
                df_copy[col] = df_copy[col].apply(self._enhanced_protect_long_numbers)

        # 特别处理"其他站点信息"字段
        if "其他站点信息" in df_copy.columns:
            df_copy["其他站点信息"] = df_copy["其他站点信息"].apply(self._enhanced_protect_long_numbers)

        # 对金额字段进行特殊处理
        for col in AMOUNT_FIELDS:
            if col in df_copy.columns:
                # 金额字段保持数值格式，但确保不使用科学计数法
                df_copy[col] = df_copy[col].apply(lambda x: f"{float(x):.2f}" if self._is_numeric(x) and pd.notna(x) else str(x))

        header = not os.path.exists(self.output_path)
        df_copy.to_csv(
            self.output_path,
            mode='a' if not header else 'w',
            header=header,
            index=False,
            encoding='utf_8_sig',
            quoting=1  # 对所有字段加引号以确保格式保持
        )

    def _is_numeric(self, value):
        """检查值是否为数字"""
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False

    def _protect_long_numbers(self, value):
        """
        保护长数字不被Excel转换为科学计数法
        增强版本：更严格的数字保护策略
        """
        if pd.isna(value) or value == '' or value == 'nan':
            return ''

        value_str = str(value).strip()

        # 如果是空字符串，直接返回
        if not value_str:
            return ''

        # 对于纯数字字符串，进行保护
        if value_str.isdigit():
            # 超过11位的数字一定要保护（手机号、身份证号等）
            if len(value_str) > 11:
                return f"'{value_str}"
            # 超过10位的数字也建议保护
            elif len(value_str) > 10:
                return f"'{value_str}"

        # 检查是否包含小数点的数字
        elif self._is_numeric(value_str):
            # 如果是浮点数且整数部分很长，也需要保护
            try:
                float_val = float(value_str)
                # 检查是否已经是科学计数法格式
                if 'e' in value_str.lower() or 'E' in value_str:
                    return f"'{value_str}"
                # 检查整数部分长度
                integer_part = str(int(float_val)) if float_val == int(float_val) else str(float_val).split('.')[0]
                if len(integer_part) > 10:
                    return f"'{value_str}"
            except (ValueError, OverflowError):
                pass

        return value_str

    def _enhanced_protect_long_numbers(self, value):
        """
        增强版长数字保护方法
        更严格的数字保护策略，防止任何可能的科学计数法转换
        """
        if pd.isna(value) or value == '' or value == 'nan':
            return ''

        value_str = str(value).strip()

        # 如果是空字符串，直接返回
        if not value_str:
            return ''

        # 检查是否已经是科学计数法格式
        if 'e' in value_str.lower():
            # 如果已经是科学计数法，保持原样但加引号保护
            return f"'{value_str}"

        # 对于纯数字字符串，进行更严格的保护
        if value_str.isdigit():
            # 超过9位的数字都要保护（更严格的标准）
            if len(value_str) > 9:
                return f"'{value_str}"
            # 对于可能被误解的特殊数字模式也要保护
            elif len(value_str) >= 8 and value_str.startswith(('1', '2', '3', '4', '5', '6', '7', '8', '9')):
                return f"'{value_str}"

        # 检查是否包含小数点的数字
        elif self._is_numeric(value_str):
            try:
                float_val = float(value_str)
                # 对于大数字，无论是否为整数都要保护
                if abs(float_val) >= 1e8:  # 大于等于1亿的数字
                    return f"'{value_str}"
                # 检查整数部分长度
                if '.' in value_str:
                    integer_part = value_str.split('.')[0]
                else:
                    integer_part = str(int(float_val)) if float_val == int(float_val) else value_str
                if len(integer_part) > 8:
                    return f"'{value_str}"
            except (ValueError, OverflowError):
                # 如果转换失败，保持原样但加引号保护
                return f"'{value_str}"

        return value_str

    def _might_be_scientific(self, value_str):
        """
        检查数字是否可能被误解为科学计数法
        """
        # 检查是否为纯数字且长度可能导致科学计数法显示
        if value_str.isdigit():
            # 长度超过11位的数字很容易被转换为科学计数法
            return len(value_str) > 11
        # 检查是否已经包含科学计数法标记
        return 'e' in value_str.lower()

    def _ensure_string_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        确保所有拆分字段都是字符串格式，防止科学计数法
        """
        df_copy = df.copy()

        # 对所有拆分字段强制转换为字符串
        for col in self.active_columns:
            if col in df_copy.columns:
                # 先转换为字符串，然后应用保护
                df_copy[col] = df_copy[col].astype(str)
                # 替换 'nan' 为空字符串
                df_copy[col] = df_copy[col].replace('nan', '')
                # 应用长数字保护
                df_copy[col] = df_copy[col].apply(self._protect_long_numbers)

        return df_copy

    def _validate_data_integrity(self, df: pd.DataFrame) -> dict:
        """
        验证数据完整性，检查是否存在科学计数法转换
        """
        validation_results = {
            'total_fields_checked': 0,
            'scientific_notation_detected': {},
            'long_numbers_preserved': 0,
            'potential_corruption': [],
            'validation_passed': True
        }

        # 检查所有拆分字段
        for col in self.active_columns:
            if col in df.columns:
                validation_results['total_fields_checked'] += 1

                # 检查科学计数法
                col_data = df[col].astype(str)
                scientific_mask = col_data.str.contains(r'[eE][+-]?\d+', na=False)

                if scientific_mask.any():
                    count = scientific_mask.sum()
                    samples = col_data[scientific_mask].head(3).tolist()
                    validation_results['scientific_notation_detected'][col] = {
                        'count': count,
                        'samples': samples
                    }

                    # 检查是否为高风险损坏
                    high_risk_samples = [s for s in samples if any(p in str(s).lower() for p in ['e+15', 'e+16', 'e+17', 'e+18', 'e+19', 'e+20'])]
                    if high_risk_samples:
                        validation_results['potential_corruption'].append({
                            'field': col,
                            'high_risk_samples': high_risk_samples
                        })
                        validation_results['validation_passed'] = False

                # 检查长数字保护
                long_number_mask = col_data.str.len() > 10
                if long_number_mask.any():
                    validation_results['long_numbers_preserved'] += long_number_mask.sum()

        return validation_results

    # ----------------- 主程序入口 -----------------
    def run_console_mode(self):
        """控制台模式运行（保留向后兼容性）"""
        try:
            print("⚠️ 注意：建议使用GUI模式获得更好的用户体验")
            print("如需使用GUI模式，请运行: python merge_dbf.py --gui")
            print("🔍 同源数据智能识别工具 - 控制台模式")

            # 这里可以添加简单的控制台文件选择逻辑
            print("控制台模式暂未实现，请使用GUI模式")
            return

        except KeyboardInterrupt:
            print("\n⚠️ 用户主动中断！")
            self.is_running = False
        except Exception as e:
            print(f"\n❌ 发生未预期错误：{str(e)}")

    def run_gui_mode(self):
        """GUI模式运行"""
        app = QApplication(sys.argv)
        window = DBFMergerGUI()
        window.show()
        sys.exit(app.exec_())


class SameSourceDataAnalyzer:
    """智能识别同源数据分析器"""

    def __init__(self, source_file_path: str, output_format: str = None):
        self.source_file = source_file_path
        self.output_format = output_format  # 'excel' 或 'csv'
        self.output_file = None  # 将在确定格式后生成
        self.data = None
        self.duplicate_groups = {}
        self.validation_results = {}
        self.customer_conflicts = []  # 存储客户身份冲突信息
        self.excel_safe_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_()[]{}. 中文字符集')
        self.customer_identity_strategy = None  # 客户身份识别策略
        self.available_customer_fields = []     # 可用的客户字段
        self.file_size_mb = 0  # 源文件大小（MB）

    def _clean_excel_string(self, text: str, max_length: int = None) -> str:
        """
        清理字符串以确保Excel兼容性
        移除控制字符、乱码字符，保留中文和常用符号
        """
        if not text or pd.isna(text):
            return ""

        text_str = str(text)

        # 移除控制字符和不可打印字符
        cleaned = ''.join(char for char in text_str if ord(char) >= 32 or char in '\t\n\r')

        # 替换可能导致Excel问题的特殊字符
        replacements = {
            '\x00': '',  # NULL字符
            '\x01': '',  # SOH
            '\x02': '',  # STX
            '\x03': '',  # ETX
            '\x04': '',  # EOT
            '\x05': '',  # ENQ
            '\x06': '',  # ACK
            '\x07': '',  # BEL
            '\x08': '',  # BS
            '\x0b': '',  # VT
            '\x0c': '',  # FF
            '\x0e': '',  # SO
            '\x0f': '',  # SI
            '\x10': '',  # DLE
            '\x11': '',  # DC1
            '\x12': '',  # DC2
            '\x13': '',  # DC3
            '\x14': '',  # DC4
            '\x15': '',  # NAK
            '\x16': '',  # SYN
            '\x17': '',  # ETB
            '\x18': '',  # CAN
            '\x19': '',  # EM
            '\x1a': '',  # SUB
            '\x1b': '',  # ESC
            '\x1c': '',  # FS
            '\x1d': '',  # GS
            '\x1e': '',  # RS
            '\x1f': '',  # US
        }

        for old_char, new_char in replacements.items():
            cleaned = cleaned.replace(old_char, new_char)

        # 移除可能的乱码字符（非UTF-8字符）
        try:
            cleaned = cleaned.encode('utf-8', errors='ignore').decode('utf-8')
        except:
            cleaned = '数据包含无效字符'

        # 限制长度
        if max_length and len(cleaned) > max_length:
            cleaned = cleaned[:max_length-3] + "..."

        return cleaned

    def _clean_worksheet_name(self, name: str) -> str:
        """
        清理工作表名称，确保Excel兼容性
        Excel工作表名称限制：
        - 最大31个字符
        - 不能包含: \\ / ? * [ ] :
        - 不能为空
        """
        if not name:
            return "数据表"

        # 清理基本字符
        cleaned = self._clean_excel_string(name, 31)

        # 移除Excel工作表名称不允许的字符
        invalid_chars = ['\\', '/', '?', '*', '[', ']', ':']
        for char in invalid_chars:
            cleaned = cleaned.replace(char, '_')

        # 确保不为空且不超过31字符
        if not cleaned.strip():
            cleaned = "数据表"
        elif len(cleaned) > 31:
            cleaned = cleaned[:28] + "..."

        return cleaned.strip()

    def _generate_analysis_filename(self, format_type: str = 'excel') -> str:
        """生成分析结果文件名"""
        base_name = os.path.splitext(self.source_file)[0]
        if format_type == 'csv':
            return f"{base_name}_智能识别同源数据分析"  # CSV会生成多个文件
        else:
            return f"{base_name}_智能识别同源数据分析.xlsx"

    def _choose_output_format(self):
        """
        选择输出格式
        根据文件大小和用户选择确定最佳输出格式
        """
        print("📊 正在分析文件大小并选择输出格式...")

        # 检查源文件大小
        if os.path.exists(self.source_file):
            file_size_bytes = os.path.getsize(self.source_file)
            self.file_size_mb = file_size_bytes / (1024 * 1024)
            print(f"   📂 源文件大小: {self.file_size_mb:.2f} MB")

        # 如果已经指定了格式，直接使用
        if self.output_format:
            print(f"   ✅ 使用指定格式: {self.output_format}")
            self.output_file = self._generate_analysis_filename(self.output_format)
            return self.output_format

        # 根据文件大小提供建议
        if self.file_size_mb > LARGE_FILE_THRESHOLD / (1024 * 1024):
            print(f"   ⚠️ 检测到大文件 (> {LARGE_FILE_THRESHOLD / (1024 * 1024):.0f} MB)")
            print(f"   💡 格式对比分析:")
            print(f"     📊 CSV格式优势:")
            print(f"       - 文件大小更小（约为Excel的30-50%）")
            print(f"       - 读写速度更快（约快3-5倍）")
            print(f"       - 内存占用更少（约为Excel的50%）")
            print(f"       - 兼容性更好，支持所有数据分析工具")
            print(f"     📊 Excel格式优势:")
            print(f"       - 多工作表支持，数据组织更清晰")
            print(f"       - 丰富的格式化和样式支持")
            print(f"       - 内置公式和图表功能")
            print(f"       - 更好的用户查看体验")

            # 自动推荐CSV格式
            print(f"   🎯 推荐使用CSV格式以获得更好的性能")

            # 简化选择：对于大文件自动使用CSV
            choice = 'csv'
            print(f"   ✅ 自动选择CSV格式（大文件优化）")
        else:
            # 小文件默认使用Excel格式
            choice = 'excel'
            print(f"   ✅ 选择Excel格式（文件大小适中）")

        self.output_format = choice
        self.output_file = self._generate_analysis_filename(choice)
        return choice

    def _analyze_customer_identity_strategy(self):
        """
        分析并确定客户身份识别策略 - 简化版本
        优先使用客户编号，如果不存在则使用客户姓名+资产账户组合
        """
        print("🔍 正在分析客户身份识别策略...")

        # 检查是否存在客户编号字段
        if PRIMARY_CUSTOMER_ID_FIELD in self.data.columns:
            # 检查客户编号的唯一性
            unique_count = self.data[PRIMARY_CUSTOMER_ID_FIELD].nunique()
            total_count = len(self.data)
            non_null_count = self.data[PRIMARY_CUSTOMER_ID_FIELD].notna().sum()

            print(f"   📊 字段 '{PRIMARY_CUSTOMER_ID_FIELD}': {unique_count} 个唯一值，{non_null_count}/{total_count} 非空记录")

            if non_null_count > 0:
                self.customer_identity_strategy = 'customer_id'
                self.available_customer_fields = [PRIMARY_CUSTOMER_ID_FIELD]
                print(f"   ✅ 采用客户编号策略: {PRIMARY_CUSTOMER_ID_FIELD}")

                # 验证客户编号的唯一性（同一客户编号下可能有多条记录，这是正常的）
                customer_id_groups = self.data.groupby(PRIMARY_CUSTOMER_ID_FIELD).size()
                max_records_per_customer = customer_id_groups.max()
                customers_with_multiple_records = (customer_id_groups > 1).sum()

                print(f"   📈 客户编号统计:")
                print(f"     - 总客户数: {unique_count}")
                print(f"     - 有多条记录的客户: {customers_with_multiple_records}")
                print(f"     - 单个客户最多记录数: {max_records_per_customer}")

                return self.customer_identity_strategy

        # 检查备用字段
        available_fallback_fields = []
        for field in FALLBACK_CUSTOMER_FIELDS:
            if field in self.data.columns:
                available_fallback_fields.append(field)
                unique_count = self.data[field].nunique()
                print(f"   📊 备用字段 '{field}': {unique_count} 个唯一值")

        if available_fallback_fields:
            self.customer_identity_strategy = 'fallback_combined'
            self.available_customer_fields = available_fallback_fields
            print(f"   ⚠️ 未发现客户编号字段，采用备用组合策略: {available_fallback_fields}")

            # 检测潜在的同名客户问题
            if '客户姓名' in self.data.columns:
                name_duplicates = self.data['客户姓名'].value_counts()
                duplicate_names = name_duplicates[name_duplicates > 1]
                if len(duplicate_names) > 0:
                    print(f"   ⚠️ 发现 {len(duplicate_names)} 个重复姓名，涉及 {duplicate_names.sum()} 条记录")
                    print(f"     重复最多的姓名: {duplicate_names.head(3).to_dict()}")
                    print(f"   💡 强烈建议：添加客户编号字段以提高识别准确性")
        else:
            raise ValueError("无法找到任何客户身份识别字段（客户编号、客户姓名、资产账户），无法进行同源数据分析")

        return self.customer_identity_strategy

    def _build_customer_identity(self, row_idx: int) -> str:
        """
        为指定行构建客户唯一标识 - 简化版本
        优先使用客户编号，如果不存在则使用客户姓名+资产账户组合
        """
        # 优先使用客户编号
        if self.customer_identity_strategy == 'customer_id' and PRIMARY_CUSTOMER_ID_FIELD in self.data.columns:
            customer_id = str(self.data.loc[row_idx, PRIMARY_CUSTOMER_ID_FIELD]).strip()
            if customer_id and customer_id != "nan" and pd.notna(customer_id):
                return customer_id

        # 备用方案：使用客户姓名+资产账户组合
        customer_identity_parts = []
        for field in self.available_customer_fields:
            if field in self.data.columns:
                value = str(self.data.loc[row_idx, field]).strip()
                if value and value != "nan" and pd.notna(value):
                    customer_identity_parts.append(f"{field}:{value}")

        if not customer_identity_parts:
            # 如果没有有效的客户信息，使用行索引作为唯一标识
            return f"ROW:{row_idx}"

        return "|".join(customer_identity_parts)

    def _detect_customer_identity_conflicts(self):
        """
        检测客户身份识别冲突 - 简化版本
        重点检查客户编号的唯一性和一致性
        """
        print("🔍 检测客户身份识别冲突...")

        conflicts = []

        if self.customer_identity_strategy == 'customer_id' and PRIMARY_CUSTOMER_ID_FIELD in self.data.columns:
            # 检查客户编号的一致性
            print("   📊 检查客户编号一致性...")

            # 检查是否有客户编号对应多个不同姓名的情况（这是异常的）
            if '客户姓名' in self.data.columns:
                customer_name_mapping = self.data.groupby(PRIMARY_CUSTOMER_ID_FIELD)['客户姓名'].nunique()
                inconsistent_customers = customer_name_mapping[customer_name_mapping > 1]

                if len(inconsistent_customers) > 0:
                    print(f"   ⚠️ 发现 {len(inconsistent_customers)} 个客户编号对应多个不同姓名（数据异常）:")
                    # 显示前5个，但存储所有冲突
                    for customer_id, name_count in inconsistent_customers.head(5).items():
                        names = self.data[self.data[PRIMARY_CUSTOMER_ID_FIELD] == customer_id]['客户姓名'].unique()
                        print(f"     - 客户编号 '{customer_id}': {name_count} 个不同姓名 {list(names)}")

                    # 存储所有冲突（不仅仅是前5个）
                    for customer_id, name_count in inconsistent_customers.items():
                        names = self.data[self.data[PRIMARY_CUSTOMER_ID_FIELD] == customer_id]['客户姓名'].unique()
                        record_count = len(self.data[self.data[PRIMARY_CUSTOMER_ID_FIELD] == customer_id])
                        conflicts.append({
                            'type': 'customer_id_inconsistency',
                            'customer_id': customer_id,
                            'name_count': name_count,
                            'names': list(names),
                            'record_count': record_count,
                            'description': f"客户编号 '{customer_id}' 对应 {name_count} 个不同姓名: {list(names)}"
                        })
                else:
                    print(f"   ✅ 客户编号与姓名映射一致")

            # 统计客户编号的记录分布（这是正常的）
            customer_record_counts = self.data[PRIMARY_CUSTOMER_ID_FIELD].value_counts()
            max_records = customer_record_counts.max()
            customers_with_multiple_records = (customer_record_counts > 1).sum()

            print(f"   📈 客户记录分布统计:")
            print(f"     - 单个客户最多记录数: {max_records}")
            print(f"     - 有多条记录的客户数: {customers_with_multiple_records}")
            print(f"   ℹ️ 注意：同一客户编号下有多条记录是正常现象")

        elif self.customer_identity_strategy == 'fallback_combined':
            # 在备用策略下检查同名不同账户的情况
            if '客户姓名' in self.data.columns and '资产账户' in self.data.columns:
                print("   📊 检查同名不同账户情况...")

                name_groups = self.data.groupby('客户姓名')
                for name, group in name_groups:
                    if len(group) > 1:  # 同名的多条记录
                        unique_accounts = group['资产账户'].nunique()
                        if unique_accounts > 1:
                            conflicts.append({
                                'type': 'same_name_different_account',
                                'name': name,
                                'record_count': len(group),
                                'account_count': unique_accounts,
                                'accounts': list(group['资产账户'].unique()),
                                'description': f"姓名 '{name}' 对应 {unique_accounts} 个不同账户: {list(group['资产账户'].unique())}"
                            })

                if conflicts:
                    print(f"   ⚠️ 发现 {len(conflicts)} 个同名不同账户情况:")
                    for conflict in conflicts[:5]:
                        print(f"     - 姓名 '{conflict['name']}': {conflict['record_count']} 条记录, "
                              f"{conflict['account_count']} 个不同账户")
                else:
                    print(f"   ✅ 未发现同名不同账户情况")

        # 存储冲突信息到类属性中
        self.customer_conflicts = conflicts
        return conflicts

    def _validate_generated_file(self):
        """
        验证生成的分析报告文件
        支持Excel和CSV格式的验证
        """
        print("📋 正在验证生成的分析报告文件...")

        if self.output_format == 'csv':
            self._validate_csv_files()
        else:
            self._validate_excel_file()

    def _validate_csv_files(self):
        """验证CSV格式的报告文件"""
        base_name = self.output_file
        expected_files = [
            f"{base_name}_分析概览.csv",
            f"{base_name}_数据验证结果.csv"
        ]

        if self.duplicate_groups:
            expected_files.append(f"{base_name}_同源数据汇总.csv")

        total_size = 0
        for file_path in expected_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                total_size += file_size
                print(f"   ✅ {os.path.basename(file_path)}: {file_size:,} 字节 ({file_size / 1024:.2f} KB)")

                # 验证CSV文件可读性
                try:
                    df = pd.read_csv(file_path, encoding='utf-8-sig', low_memory=False, dtype=str)
                    print(f"     📊 内容: {len(df)} 行 x {len(df.columns)} 列")
                except Exception as e:
                    print(f"     ❌ 文件读取失败: {str(e)}")
            else:
                print(f"   ❌ 缺少文件: {os.path.basename(file_path)}")

        print(f"   📊 CSV报告总大小: {total_size:,} 字节 ({total_size / 1024:.2f} KB)")

        if total_size < MIN_EXCEL_FILE_SIZE:
            print(f"   ⚠️ 警告：报告总大小异常小")
        else:
            print(f"   ✅ CSV报告文件验证通过")

    def _validate_excel_file(self):
        """验证Excel格式的报告文件"""
        if not os.path.exists(self.output_file):
            raise FileNotFoundError(f"分析报告文件未生成: {self.output_file}")

        # 检查文件大小
        file_size = os.path.getsize(self.output_file)
        print(f"   📊 文件大小: {file_size:,} 字节 ({file_size / 1024:.2f} KB)")

        if file_size < MIN_EXCEL_FILE_SIZE:
            print(f"   ⚠️ 警告：文件大小异常小 (< {MIN_EXCEL_FILE_SIZE / 1024:.0f} KB)")
            print(f"   🔍 可能原因：数据写入不完整或Excel生成失败")
        elif file_size > MAX_EXCEL_FILE_SIZE:
            print(f"   ⚠️ 警告：文件大小过大 (> {MAX_EXCEL_FILE_SIZE / 1024 / 1024:.0f} MB)")
            print(f"   💡 建议：考虑使用CSV格式")
        else:
            print(f"   ✅ 文件大小正常")

        # 验证Excel文件可读性
        try:
            import openpyxl
            wb = openpyxl.load_workbook(self.output_file)
            worksheet_names = wb.sheetnames
            print(f"   📋 工作表: {worksheet_names}")

            # 检查每个工作表的内容
            for sheet_name in worksheet_names:
                ws = wb[sheet_name]
                max_row = ws.max_row
                max_col = ws.max_column
                print(f"     - {sheet_name}: {max_row} 行 x {max_col} 列")

                if max_row <= 1:
                    print(f"       ⚠️ 工作表 '{sheet_name}' 内容为空或只有表头")

            # 验证预期的工作表是否存在
            expected_sheets = ["分析概览", "数据验证结果"]
            if self.duplicate_groups:
                expected_sheets.append("同源数据汇总")

            missing_sheets = [sheet for sheet in expected_sheets if sheet not in worksheet_names]

            if missing_sheets:
                print(f"   ⚠️ 缺少预期工作表: {missing_sheets}")
            else:
                print(f"   ✅ 所有预期工作表都已生成")

            wb.close()
            print(f"   ✅ Excel文件验证通过")

        except Exception as e:
            print(f"   ❌ Excel文件验证失败: {str(e)}")
            raise Exception(f"生成的Excel文件损坏或无法读取: {str(e)}")

    def run_analysis(self):
        """执行完整的分析流程"""
        try:
            print("📊 智能识别同源数据 - 开始分析")

            # 1. 选择输出格式
            self._choose_output_format()

            # 2. 加载数据
            self._load_data()

            # 3. 分析客户身份识别策略
            self._analyze_customer_identity_strategy()

            # 4. 检测客户身份冲突
            self._detect_customer_identity_conflicts()

            # 5. 数据清理和验证
            self._clean_and_validate_data()

            # 6. 识别重复数据
            self._identify_duplicates()

            # 7. 生成分析报告
            self._generate_report()

            # 8. 验证生成的文件
            self._validate_generated_file()

            print(f"✅ 智能识别同源数据分析完成！")
            if self.output_format == 'csv':
                print(f"📄 分析报告保存为CSV格式：{self.output_file}_*.csv")
            else:
                print(f"📄 分析报告保存在：{self.output_file}")

        except Exception as e:
            error_msg = str(e)
            print(f"❌ 智能识别同源数据分析失败：{error_msg}")

            # 提供详细的错误诊断信息
            if "cannot be used in worksheets" in error_msg.lower():
                print("🔍 错误分析：数据包含Excel不支持的特殊字符")
                print("💡 建议：尝试使用CSV格式输出")
            elif "invalid character" in error_msg.lower():
                print("🔍 错误分析：数据包含无效字符")
                print("💡 建议：数据可能存在编码问题或包含二进制字符")
            elif "memory" in error_msg.lower():
                print("🔍 错误分析：内存不足")
                print("💡 建议：使用CSV格式或分批处理")
            else:
                print("🔍 错误分析：未知错误类型")
                print("💡 建议：检查数据格式和系统环境")

            # 尝试生成简化的诊断报告
            try:
                self._generate_diagnostic_report(error_msg)
            except:
                pass  # 如果诊断报告也失败，就不生成了

    def _generate_diagnostic_report(self, error_msg: str):
        """生成诊断报告"""
        print("🔧 正在生成错误诊断报告...")

        diagnostic_file = self.output_file.replace('.xlsx', '_错误诊断.txt')

        with open(diagnostic_file, 'w', encoding='utf-8') as f:
            f.write("智能识别同源数据分析 - 错误诊断报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"源文件: {self.source_file}\n")
            f.write(f"错误信息: {error_msg}\n\n")

            if self.data is not None:
                f.write(f"数据基本信息:\n")
                f.write(f"- 总行数: {len(self.data)}\n")
                f.write(f"- 总列数: {len(self.data.columns)}\n")
                f.write(f"- 列名: {list(self.data.columns)}\n\n")

                # 检查数据中的特殊字符
                f.write("数据质量检查:\n")
                problematic_columns = []

                for col in self.data.columns:
                    if self.data[col].dtype == 'object':
                        # 检查是否包含控制字符
                        has_control_chars = False
                        sample_values = []

                        for idx, value in self.data[col].head(10).items():
                            if pd.notna(value):
                                value_str = str(value)
                                if any(ord(char) < 32 and char not in '\t\n\r' for char in value_str):
                                    has_control_chars = True
                                    sample_values.append(f"行{idx}: {repr(value_str[:50])}")

                        if has_control_chars:
                            problematic_columns.append(col)
                            f.write(f"- 列 '{col}' 包含控制字符\n")
                            for sample in sample_values[:3]:
                                f.write(f"  {sample}\n")

                if not problematic_columns:
                    f.write("- 未发现明显的数据质量问题\n")

            f.write(f"\n建议解决方案:\n")
            f.write(f"1. 检查源数据是否包含乱码或特殊字符\n")
            f.write(f"2. 尝试重新导出源数据，确保编码正确\n")
            f.write(f"3. 使用数据清理工具预处理数据\n")
            f.write(f"4. 如问题持续，请联系技术支持\n")

        print(f"📄 诊断报告已保存: {diagnostic_file}")

    def _load_data(self):
        """加载数据文件"""
        print("📂 正在加载数据文件...")

        try:
            if self.source_file.endswith('.parquet'):
                self.data = pd.read_parquet(self.source_file)
                print("   ✅ Parquet格式文件加载成功")
            elif self.source_file.endswith('.csv'):
                # 解决pandas DtypeWarning问题
                # 方案：指定低内存模式为False，让pandas自动推断最佳数据类型
                print("   🔄 正在分析CSV文件结构...")
                self.data = pd.read_csv(
                    self.source_file,
                    encoding='utf-8-sig',
                    low_memory=False,  # 解决DtypeWarning
                    dtype=str,  # 将所有列都读取为字符串，避免类型推断警告
                    na_values=['', 'NA', 'N/A', 'null', 'NULL', 'None'],  # 明确指定NA值
                    keep_default_na=True
                )
                print("   ✅ CSV格式文件加载成功")
            else:
                raise ValueError("不支持的文件格式，仅支持CSV和Parquet格式")

            # 数据基本信息
            print(f"✅ 数据加载完成:")
            print(f"   - 总行数: {len(self.data):,}")
            print(f"   - 总列数: {len(self.data.columns)}")
            print(f"   - 内存使用: {self.data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")

            # 检查关键字段是否存在
            all_customer_fields = [PRIMARY_CUSTOMER_ID_FIELD] + FALLBACK_CUSTOMER_FIELDS
            missing_customer_fields = [field for field in all_customer_fields if field not in self.data.columns]
            missing_check_fields = [field for field in DUPLICATE_CHECK_FIELDS if field not in self.data.columns]

            if len(missing_customer_fields) == len(all_customer_fields):
                print(f"   ❌ 缺少所有客户字段: {all_customer_fields}")
            elif missing_customer_fields:
                print(f"   ⚠️ 缺少部分客户字段: {missing_customer_fields}")
            if missing_check_fields:
                print(f"   ℹ️ 缺少检查字段: {missing_check_fields} (将跳过这些字段)")

            available_check_fields = [field for field in DUPLICATE_CHECK_FIELDS if field in self.data.columns]
            print(f"   📋 可用检查字段: {len(available_check_fields)} 个")

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            raise

    def _clean_and_validate_data(self):
        """数据清理和验证 - 增强版本"""
        print("🧹 开始数据清理和验证流程...")

        validation_issues = []
        validation_stats = {
            'scientific_notation_checks': 0,
            'format_validations': 0,
            'integrity_validations': 0,
            'total_cells_checked': 0
        }

        # 1. 检查科学计数法
        print("   🔍 步骤1: 检查科学计数法格式...")
        scientific_notation_issues = self._check_scientific_notation()
        validation_stats['scientific_notation_checks'] = len(DUPLICATE_CHECK_FIELDS)
        if scientific_notation_issues:
            validation_issues.extend(scientific_notation_issues)
            print(f"     ⚠️ 发现 {len(scientific_notation_issues)} 个科学计数法问题")
        else:
            print(f"     ✅ 科学计数法检查通过")

        # 2. 验证数据格式
        print("   📊 步骤2: 验证数据格式...")
        format_issues = self._validate_data_formats()
        validation_stats['format_validations'] = len(AMOUNT_FIELDS) + len(DUPLICATE_CHECK_FIELDS)
        if format_issues:
            validation_issues.extend(format_issues)
            print(f"     ⚠️ 发现 {len(format_issues)} 个格式问题")
        else:
            print(f"     ✅ 数据格式验证通过")

        # 3. 验证站点地址数据完整性
        print("   🔗 步骤3: 验证站点地址数据完整性...")
        address_issues = self._validate_site_address_integrity()
        validation_stats['integrity_validations'] = len(self.data) if SPLIT_FIELD in self.data.columns else 0
        if address_issues:
            validation_issues.extend(address_issues)
            print(f"     ⚠️ 发现 {len(address_issues)} 个完整性问题")
        else:
            print(f"     ✅ 站点地址完整性验证通过")

        # 4. 计算数据质量评分
        total_checks = sum(validation_stats.values())
        issue_weight = 2  # 每个问题扣2分
        quality_score = max(0, 100 - len(validation_issues) * issue_weight)

        self.validation_results = {
            'total_issues': len(validation_issues),
            'issues': validation_issues,
            'data_quality_score': quality_score,
            'validation_stats': validation_stats
        }

        print(f"✅ 数据清理和验证完成:")
        print(f"   - 执行检查项: {total_checks} 项")
        print(f"   - 发现问题: {len(validation_issues)} 个")
        print(f"   - 数据质量评分: {quality_score}/100")

        if validation_issues:
            print(f"   📋 主要问题类型:")
            issue_types = {}
            for issue in validation_issues:
                if "科学计数法" in issue:
                    issue_types["科学计数法"] = issue_types.get("科学计数法", 0) + 1
                elif "格式" in issue:
                    issue_types["数据格式"] = issue_types.get("数据格式", 0) + 1
                elif "完整性" in issue:
                    issue_types["数据完整性"] = issue_types.get("数据完整性", 0) + 1
                else:
                    issue_types["其他"] = issue_types.get("其他", 0) + 1

            for issue_type, count in issue_types.items():
                print(f"     - {issue_type}: {count} 个")
        else:
            print(f"   🎉 数据质量优秀，未发现问题！")

    def _check_scientific_notation(self) -> list:
        """
        检查科学计数法问题 - 增强版本
        提供更详细的分析和数据完整性检查
        """
        issues = []
        detailed_stats = {}

        print("   🔍 正在进行科学计数法检测...")

        for col in self.data.columns:
            if col in DUPLICATE_CHECK_FIELDS:
                # 检查是否存在科学计数法格式的数据
                col_data = self.data[col].astype(str)
                scientific_mask = col_data.str.contains(r'[eE][+-]?\d+', na=False)

                if scientific_mask.any():
                    count = scientific_mask.sum()

                    # 收集样本数据进行分析
                    scientific_samples = col_data[scientific_mask].head(5).tolist()

                    # 分析是否为数据损坏
                    corruption_analysis = self._analyze_scientific_notation_corruption(scientific_samples)

                    detailed_stats[col] = {
                        'count': count,
                        'samples': scientific_samples,
                        'corruption_risk': corruption_analysis['risk_level'],
                        'analysis': corruption_analysis['analysis']
                    }

                    if corruption_analysis['risk_level'] == 'HIGH':
                        issues.append(f"⚠️ 字段 '{col}' 发现 {count} 个科学计数法格式数据 - 高风险数据损坏")
                    elif corruption_analysis['risk_level'] == 'MEDIUM':
                        issues.append(f"⚠️ 字段 '{col}' 发现 {count} 个科学计数法格式数据 - 中等风险")
                    else:
                        issues.append(f"ℹ️ 字段 '{col}' 发现 {count} 个科学计数法格式数据 - 可能为正常数据")

                    print(f"     - {col}: {count} 个科学计数法值，风险等级: {corruption_analysis['risk_level']}")
                    print(f"       样本: {scientific_samples[:3]}")

        # 保存详细统计信息供后续使用
        self.scientific_notation_stats = detailed_stats

        return issues

    def _analyze_scientific_notation_corruption(self, samples: list) -> dict:
        """
        分析科学计数法数据是否为数据损坏
        """
        if not samples:
            return {'risk_level': 'LOW', 'analysis': '无样本数据'}

        high_risk_patterns = 0
        medium_risk_patterns = 0

        for sample in samples:
            sample_str = str(sample).lower()

            # 高风险模式：明显的长数字损坏
            if any(pattern in sample_str for pattern in ['e+20', 'e+19', 'e+18', 'e+17', 'e+16', 'e+15']):
                high_risk_patterns += 1
            # 中等风险模式：可能的数字损坏
            elif any(pattern in sample_str for pattern in ['e+14', 'e+13', 'e+12', 'e+11', 'e+10']):
                medium_risk_patterns += 1

        total_samples = len(samples)
        high_risk_ratio = high_risk_patterns / total_samples
        medium_risk_ratio = medium_risk_patterns / total_samples

        if high_risk_ratio > 0.5:
            return {
                'risk_level': 'HIGH',
                'analysis': f'检测到 {high_risk_patterns}/{total_samples} 个高风险科学计数法模式，可能存在严重数据损坏'
            }
        elif high_risk_ratio > 0.2 or medium_risk_ratio > 0.5:
            return {
                'risk_level': 'MEDIUM',
                'analysis': f'检测到 {high_risk_patterns + medium_risk_patterns}/{total_samples} 个可疑科学计数法模式'
            }
        else:
            return {
                'risk_level': 'LOW',
                'analysis': '科学计数法模式看起来正常，可能为合法的科学计数法数据'
            }

    def _validate_data_formats(self) -> list:
        """验证数据格式"""
        issues = []

        # 检查金额字段是否为数值类型
        for field in AMOUNT_FIELDS:
            if field in self.data.columns:
                try:
                    pd.to_numeric(self.data[field], errors='coerce')
                except:
                    issues.append(f"金额字段 '{field}' 格式验证失败")

        # 检查其他字段是否为文本格式
        for field in DUPLICATE_CHECK_FIELDS:
            if field in self.data.columns:
                non_text_count = 0
                for value in self.data[field].dropna():
                    if not isinstance(value, str):
                        non_text_count += 1

                if non_text_count > 0:
                    issues.append(f"字段 '{field}' 发现 {non_text_count} 个非文本格式数据")

        return issues

    def _validate_site_address_integrity(self) -> list:
        """验证站点地址数据完整性"""
        issues = []

        if SPLIT_FIELD not in self.data.columns:
            issues.append(f"缺少原始站点地址字段 '{SPLIT_FIELD}'")
            return issues

        # 检查拆分字段的数据是否包含在原始字段中
        validation_count = 0
        integrity_issues = 0

        for idx, row in self.data.iterrows():
            original_address = str(row.get(SPLIT_FIELD, "")).strip()
            if not original_address or original_address == "nan":
                continue

            validation_count += 1

            # 检查拆分出的字段值是否在原始地址中
            for field in DUPLICATE_CHECK_FIELDS:
                if field in self.data.columns:
                    field_value = str(row.get(field, "")).strip()
                    if field_value and field_value != "nan" and field_value != "NA":
                        # 移除符号进行比较
                        clean_value = self._clean_value_for_comparison(field_value)
                        clean_original = self._clean_value_for_comparison(original_address)

                        if clean_value not in clean_original:
                            integrity_issues += 1
                            break

        if integrity_issues > 0:
            issues.append(f"站点地址完整性验证：{integrity_issues}/{validation_count} 行存在数据不一致")

        return issues

    def _clean_value_for_comparison(self, value: str) -> str:
        """
        清理值用于比较，移除所有符号
        完全忽略所有符号字符，只保留字母和数字进行比较
        """
        if not value or value == "nan" or pd.isna(value):
            return ""

        value_str = str(value).strip()
        if not value_str:
            return ""

        # 移除所有符号字符，只保留字母、数字和中文字符
        # 包括但不限于：' " - : _ . , ; | \ / @ # $ % ^ & * ( ) [ ] { } < > + = ~ `
        cleaned = re.sub(r"[^a-zA-Z0-9\u4e00-\u9fff]", "", value_str)

        # 转换为大写并去除空白
        result = cleaned.upper().strip()

        return result

    def _is_false_positive_value(self, field: str, value: str) -> bool:
        """
        检查值是否为已知的误报值
        """
        if field not in FALSE_POSITIVE_VALUES:
            return False

        # 清理值进行比较（移除符号）
        clean_value = self._clean_value_for_comparison(value)

        # 检查是否在误报值列表中
        for false_positive in FALSE_POSITIVE_VALUES[field]:
            clean_false_positive = self._clean_value_for_comparison(false_positive)
            if clean_value == clean_false_positive:
                return True

        return False

    def _identify_duplicates(self):
        """
        识别同源数据 - 重构版本（增加误报值过滤）
        同源数据定义：相同的站点地址字段值对应多个不同客户的情况
        """
        print("🔍 开始智能识别同源数据...")
        print("📋 同源数据定义：相同字段值被多个不同客户使用")
        print("🚫 将排除已知误报值以提高分析准确性")

        duplicate_groups = {}
        total_processed_fields = 0
        total_comparison_count = 0
        symbol_cleaning_stats = {'before': 0, 'after': 0, 'cleaned': 0}
        false_positive_stats = {'total_excluded': 0, 'by_field': {}}

        available_fields = [field for field in DUPLICATE_CHECK_FIELDS if field in self.data.columns]
        print(f"📊 将检查 {len(available_fields)} 个字段：{available_fields}")

        # 为每个检查字段创建值到行索引的映射
        for field_idx, field in enumerate(available_fields, 1):
            print(f"\n🔍 [{field_idx}/{len(available_fields)}] 正在分析字段: {field}")

            field_groups = defaultdict(list)
            field_symbol_stats = {'original_values': 0, 'cleaned_values': 0, 'symbol_removed': 0}
            field_false_positive_count = 0

            # 第一步：按清理后的值分组
            print(f"   📝 步骤1: 收集并清理字段值...")
            for idx, row in self.data.iterrows():
                original_value = str(row.get(field, "")).strip()
                if original_value and original_value != "nan" and original_value != "NA":
                    field_symbol_stats['original_values'] += 1

                    # 检查是否为误报值
                    if self._is_false_positive_value(field, original_value):
                        field_false_positive_count += 1
                        continue  # 跳过误报值

                    # 清理符号进行比较
                    clean_value = self._clean_value_for_comparison(original_value)
                    if clean_value:
                        field_symbol_stats['cleaned_values'] += 1
                        if clean_value != original_value.upper().strip():
                            field_symbol_stats['symbol_removed'] += 1

                        field_groups[clean_value].append({
                            'row_idx': idx,
                            'original_value': original_value,
                            'clean_value': clean_value
                        })

            # 更新误报统计
            false_positive_stats['by_field'][field] = field_false_positive_count
            false_positive_stats['total_excluded'] += field_false_positive_count

            print(f"   📊 字段统计: 原始值 {field_symbol_stats['original_values']} 个, "
                  f"有效值 {field_symbol_stats['cleaned_values']} 个, "
                  f"符号清理 {field_symbol_stats['symbol_removed']} 个, "
                  f"排除误报 {field_false_positive_count} 个")

            # 第二步：识别多客户共享的值
            print(f"   🔍 步骤2: 识别多客户共享情况...")
            potential_duplicates = 0
            confirmed_duplicates = 0

            for clean_value, value_records in field_groups.items():
                if len(value_records) > 1:
                    potential_duplicates += 1

                    # 构建客户标识集合 - 修复核心逻辑错误
                    customer_identities = set()
                    customer_details = {}

                    for record in value_records:
                        idx = record['row_idx']
                        # 使用新的客户身份识别方法
                        customer_identity = self._build_customer_identity(idx)

                        if customer_identity:
                            customer_identities.add(customer_identity)

                            # 保存客户详细信息用于报告
                            if customer_identity not in customer_details:
                                customer_details[customer_identity] = {}
                                for customer_field in self.available_customer_fields:
                                    if customer_field in self.data.columns:
                                        customer_value = str(self.data.loc[idx, customer_field]).strip()
                                        if customer_value and customer_value != "nan":
                                            customer_details[customer_identity][customer_field] = customer_value

                    # 只有当存在多个不同客户时才认为是同源数据
                    if len(customer_identities) > 1:
                        confirmed_duplicates += 1

                        if field not in duplicate_groups:
                            duplicate_groups[field] = []

                        # 收集所有相关行的索引
                        all_row_indices = [record['row_idx'] for record in value_records]
                        all_original_values = [record['original_value'] for record in value_records]

                        duplicate_groups[field].append({
                            'clean_value': clean_value,
                            'original_values': list(set(all_original_values)),  # 去重
                            'row_indices': all_row_indices,
                            'customer_identities': list(customer_identities),
                            'customer_details': customer_details,
                            'customer_count': len(customer_identities),
                            'record_count': len(value_records)
                        })

                        print(f"     ✅ 发现同源数据: 值'{clean_value}' 被 {len(customer_identities)} 个不同客户使用 "
                              f"(共 {len(value_records)} 条记录)")

            print(f"   📈 字段结果: 潜在重复 {potential_duplicates} 组, 确认同源 {confirmed_duplicates} 组")

            # 更新统计信息
            total_processed_fields += 1
            total_comparison_count += len(field_groups)
            symbol_cleaning_stats['before'] += field_symbol_stats['original_values']
            symbol_cleaning_stats['after'] += field_symbol_stats['cleaned_values']
            symbol_cleaning_stats['cleaned'] += field_symbol_stats['symbol_removed']

        self.duplicate_groups = duplicate_groups

        # 输出详细的最终统计
        total_duplicate_groups = sum(len(groups) for groups in duplicate_groups.values())
        total_affected_records = sum(
            sum(len(group['row_indices']) for group in groups)
            for groups in duplicate_groups.values()
        )

        print(f"\n✅ 同源数据识别完成!")
        print(f"📊 处理统计:")
        print(f"   - 检查字段: {total_processed_fields} 个")
        print(f"   - 符号清理: {symbol_cleaning_stats['cleaned']}/{symbol_cleaning_stats['before']} 个值")
        print(f"   - 排除误报值: {false_positive_stats['total_excluded']} 个")
        print(f"   - 发现同源数据组: {total_duplicate_groups} 组")
        print(f"   - 涉及记录数: {total_affected_records} 条")

        # 显示误报值排除详情
        if false_positive_stats['total_excluded'] > 0:
            print(f"🚫 误报值排除详情:")
            for field, count in false_positive_stats['by_field'].items():
                if count > 0:
                    excluded_values = FALSE_POSITIVE_VALUES.get(field, [])
                    print(f"   - {field}: 排除 {count} 个值 {excluded_values}")

        if duplicate_groups:
            print(f"📋 同源数据详情:")
            for field, groups in duplicate_groups.items():
                print(f"   - {field}: {len(groups)} 组同源数据")
        else:
            print(f"✨ 未发现同源数据，所有字段值都是客户独有的")

    def _generate_report(self):
        """生成分析报告"""
        print("📊 正在生成分析报告...")

        if self.output_format == 'csv':
            # 直接生成CSV格式报告
            try:
                self._generate_csv_report()
                print(f"✅ CSV格式分析报告生成完成")
            except Exception as e:
                print(f"❌ CSV报告生成失败: {str(e)}")
                raise
        else:
            # 生成Excel格式报告
            try:
                # 预处理数据，清理特殊字符
                self._preprocess_data_for_excel()

                wb = Workbook()

                # 删除默认工作表
                wb.remove(wb.active)

                # 1. 创建概览工作表
                self._create_overview_sheet(wb)

                # 2. 创建数据验证工作表
                self._create_validation_sheet(wb)

                # 3. 为每个字段的重复数据创建工作表
                self._create_duplicate_sheets(wb)

                # 保存工作簿
                wb.save(self.output_file)
                print(f"✅ Excel格式分析报告生成完成")

            except Exception as e:
                print(f"⚠️ Excel报告生成失败: {str(e)}")
                print("🔄 尝试生成CSV格式备用报告...")

                # 优雅降级：生成CSV格式报告
                try:
                    self._generate_csv_fallback_report()
                    print("✅ CSV格式备用报告生成完成")
                except Exception as csv_error:
                    print(f"❌ CSV备用报告也生成失败: {str(csv_error)}")
                    raise Exception(f"报告生成完全失败 - Excel错误: {str(e)}, CSV错误: {str(csv_error)}")

    def _preprocess_data_for_excel(self):
        """预处理数据以确保Excel兼容性"""
        print("🧹 正在清理数据以确保Excel兼容性...")

        if self.data is None:
            return

        # 清理所有字符串列的数据
        for column in self.data.columns:
            if self.data[column].dtype == 'object':  # 字符串列
                self.data[column] = self.data[column].apply(
                    lambda x: self._clean_excel_string(x, 32767) if pd.notna(x) else ""
                )

        # 清理重复组数据中的字符串
        for field, groups in self.duplicate_groups.items():
            for group in groups:
                # 清理original_values
                if 'original_values' in group:
                    group['original_values'] = [
                        self._clean_excel_string(val) for val in group['original_values']
                    ]

                # 清理customers
                if 'customers' in group:
                    group['customers'] = [
                        self._clean_excel_string(customer) for customer in group['customers']
                    ]

        print("✅ 数据清理完成")

    def _generate_csv_fallback_report(self):
        """生成CSV格式的备用报告"""
        base_name = os.path.splitext(self.output_file)[0]

        # 1. 生成概览CSV
        overview_file = f"{base_name}_分析概览.csv"
        overview_data = []

        overview_data.append(['分析项目', '结果'])
        overview_data.append(['分析时间', time.strftime('%Y-%m-%d %H:%M:%S')])
        overview_data.append(['源文件', os.path.basename(self.source_file)])
        overview_data.append(['总记录数', len(self.data)])
        overview_data.append(['数据质量评分', f"{self.validation_results['data_quality_score']}/100"])
        overview_data.append(['发现问题数', self.validation_results['total_issues']])

        for field, groups in self.duplicate_groups.items():
            overview_data.append([f'{field}字段重复', f'{len(groups)}组'])

        pd.DataFrame(overview_data).to_csv(overview_file, index=False, header=False, encoding='utf-8-sig')

        # 2. 生成验证问题CSV
        if self.validation_results.get('issues'):
            validation_file = f"{base_name}_数据验证结果.csv"
            validation_data = []
            validation_data.append(['序号', '问题类型', '问题描述', '严重程度'])

            for i, issue in enumerate(self.validation_results['issues'], 1):
                severity = "中等" if "科学计数法" in issue else "低"
                validation_data.append([i, "数据格式", issue, severity])

            pd.DataFrame(validation_data).to_csv(validation_file, index=False, header=False, encoding='utf-8-sig')

        # 3. 生成重复数据CSV
        if self.duplicate_groups:
            duplicate_file = f"{base_name}_同源数据汇总.csv"

            # 收集所有重复数据行
            all_duplicate_rows = set()
            for field, groups in self.duplicate_groups.items():
                for group in groups:
                    all_duplicate_rows.update(group['row_indices'])

            if all_duplicate_rows:
                duplicate_data = self.data.loc[list(all_duplicate_rows)].copy()

                # 添加标识列
                duplicate_data.insert(0, '重复字段', '')
                duplicate_data.insert(1, '重复值', '')
                duplicate_data.insert(2, '客户数量', '')

                # 填充标识信息
                for field, groups in self.duplicate_groups.items():
                    for group in groups:
                        for idx in group['row_indices']:
                            if idx in duplicate_data.index:
                                duplicate_data.loc[idx, '重复字段'] = field
                                duplicate_data.loc[idx, '重复值'] = group['original_values'][0] if group['original_values'] else ''
                                duplicate_data.loc[idx, '客户数量'] = group['customer_count']

                duplicate_data.to_csv(duplicate_file, index=False, encoding='utf-8-sig')

        print(f"📄 CSV报告文件保存在：{base_name}_*.csv")

    def _generate_csv_report(self):
        """生成主要的CSV格式报告"""
        print("   📊 正在生成CSV格式分析报告...")

        base_name = self.output_file

        # 1. 生成分析概览CSV
        overview_file = f"{base_name}_分析概览.csv"
        self._create_overview_csv(overview_file)

        # 2. 生成数据验证结果CSV
        validation_file = f"{base_name}_数据验证结果.csv"
        self._create_validation_csv(validation_file)

        # 3. 生成同源数据CSV文件（按字段分组）
        if self.duplicate_groups:
            self._create_field_specific_csv_files(base_name)
            # 同时生成汇总文件
            duplicate_file = f"{base_name}_同源数据汇总.csv"
            self._create_duplicate_csv(duplicate_file)

        print(f"   ✅ CSV报告生成完成，包含以下文件:")
        print(f"     - {os.path.basename(overview_file)}")
        print(f"     - {os.path.basename(validation_file)}")
        if self.duplicate_groups:
            print(f"     - {os.path.basename(duplicate_file)}")

    def _create_overview_csv(self, filename: str):
        """创建概览CSV文件"""
        overview_data = []

        # 基本信息
        overview_data.append(['分析项目', '结果'])
        overview_data.append(['分析时间', time.strftime('%Y-%m-%d %H:%M:%S')])
        overview_data.append(['源文件', os.path.basename(self.source_file)])
        overview_data.append(['源文件大小(MB)', f"{self.file_size_mb:.2f}"])
        overview_data.append(['总记录数', len(self.data)])
        overview_data.append(['客户身份识别策略', self.customer_identity_strategy])
        overview_data.append(['使用字段', ', '.join(self.available_customer_fields)])
        overview_data.append(['数据质量评分', f"{self.validation_results['data_quality_score']}/100"])
        overview_data.append(['发现问题数', self.validation_results['total_issues']])

        # 同源数据统计
        overview_data.append(['', ''])  # 空行
        overview_data.append(['同源数据统计', ''])
        total_groups = sum(len(groups) for groups in self.duplicate_groups.values())
        overview_data.append(['总同源数据组数', total_groups])

        for field, groups in self.duplicate_groups.items():
            overview_data.append([f'{field}字段同源数据组', len(groups)])

        # 保存CSV
        pd.DataFrame(overview_data).to_csv(filename, index=False, header=False, encoding='utf-8-sig')

    def _create_validation_csv(self, filename: str):
        """创建验证结果CSV文件 - 增强版本，包含客户身份冲突详情"""
        validation_data = []
        validation_data.append(['序号', '问题类型', '问题描述', '严重程度', '详细信息'])

        row_num = 1

        # 添加数据格式问题
        for issue in self.validation_results['issues']:
            severity = "中等" if "科学计数法" in issue else "低"
            validation_data.append([row_num, "数据格式", issue, severity, ""])
            row_num += 1

        # 添加客户身份冲突问题
        if hasattr(self, 'customer_conflicts') and self.customer_conflicts:
            validation_data.append([row_num, "", "", "", ""])  # 空行分隔
            row_num += 1
            validation_data.append([row_num, "客户身份冲突", "以下为检测到的所有客户身份冲突详情", "高", ""])
            row_num += 1

            for conflict in self.customer_conflicts:
                if conflict['type'] == 'customer_id_inconsistency':
                    validation_data.append([
                        row_num,
                        "客户编号冲突",
                        conflict['description'],
                        "高",
                        f"记录数: {conflict['record_count']}, 姓名列表: {', '.join(conflict['names'])}"
                    ])
                elif conflict['type'] == 'same_name_different_account':
                    validation_data.append([
                        row_num,
                        "同名不同账户",
                        conflict['description'],
                        "中等",
                        f"记录数: {conflict['record_count']}, 账户列表: {', '.join(conflict['accounts'])}"
                    ])
                row_num += 1

        if len(validation_data) == 1:  # 如果只有表头，说明没有问题
            validation_data.append([1, "数据质量", "未发现数据质量问题", "无", ""])

        pd.DataFrame(validation_data).to_csv(filename, index=False, header=False, encoding='utf-8-sig')

    def _create_duplicate_csv(self, filename: str):
        """创建同源数据CSV文件"""
        # 收集所有重复数据行
        all_duplicate_rows = set()
        for field, groups in self.duplicate_groups.items():
            for group in groups:
                all_duplicate_rows.update(group['row_indices'])

        if all_duplicate_rows:
            duplicate_data = self.data.loc[list(all_duplicate_rows)].copy()

            # 添加标识列
            duplicate_data.insert(0, '重复字段', '')
            duplicate_data.insert(1, '重复值', '')
            duplicate_data.insert(2, '客户数量', '')
            duplicate_data.insert(3, '客户身份', '')

            # 填充标识信息
            for field, groups in self.duplicate_groups.items():
                for group in groups:
                    for idx in group['row_indices']:
                        if idx in duplicate_data.index:
                            duplicate_data.loc[idx, '重复字段'] = field
                            duplicate_data.loc[idx, '重复值'] = group['original_values'][0] if group['original_values'] else ''
                            duplicate_data.loc[idx, '客户数量'] = group['customer_count']
                            duplicate_data.loc[idx, '客户身份'] = self._build_customer_identity(idx)

            # 按重复字段和重复值排序，确保相同值的记录相邻，与字段专用文件保持一致
            duplicate_data = duplicate_data.sort_values(['重复字段', '重复值', '客户身份'])

            duplicate_data.to_csv(filename, index=False, encoding='utf-8-sig')

    def _create_field_specific_csv_files(self, base_name: str):
        """为每个字段创建单独的CSV文件"""
        for field, groups in self.duplicate_groups.items():
            if not groups:
                continue

            # 创建字段专用CSV文件
            field_file = f"{base_name}_{field}字段同源数据.csv"

            # 收集该字段的所有重复数据
            field_duplicate_data = []

            # 按重复值分组并排序
            sorted_groups = sorted(groups, key=lambda g: g['clean_value'])

            for group in sorted_groups:
                # 获取该组的所有记录
                group_rows = self.data.loc[group['row_indices']].copy()

                # 为每行添加重复信息
                for idx in group['row_indices']:
                    if idx in group_rows.index:
                        row_data = group_rows.loc[idx].copy()
                        # 创建新的行字典
                        row_dict = {
                            '重复字段': field,
                            '重复值': group['original_values'][0] if group['original_values'] else '',
                            '客户数量': group['customer_count'],
                            '客户身份': self._build_customer_identity(idx)
                        }
                        # 添加原始数据
                        for col, val in row_data.items():
                            row_dict[col] = val
                        field_duplicate_data.append(row_dict)

            if field_duplicate_data:
                # 转换为DataFrame并按重复值排序
                field_df = pd.DataFrame(field_duplicate_data)
                field_df = field_df.sort_values(['重复值', '客户身份'])

                # 保存CSV文件
                field_df.to_csv(field_file, index=False, encoding='utf-8-sig')
                print(f"     - {os.path.basename(field_file)}")

    def _create_overview_sheet(self, wb: Workbook):
        """创建概览工作表"""
        ws = wb.create_sheet("分析概览", 0)

        # 标题样式
        title_font = Font(size=14, bold=True)
        header_font = Font(size=12, bold=True)
        header_fill = PatternFill(start_color="E6E6FA", end_color="E6E6FA", fill_type="solid")

        # 写入标题
        ws['A1'] = "智能识别同源数据分析报告"
        ws['A1'].font = title_font
        ws.merge_cells('A1:D1')

        # 基本信息
        row = 3
        ws[f'A{row}'] = "分析时间："
        ws[f'B{row}'] = time.strftime('%Y-%m-%d %H:%M:%S')
        row += 1

        ws[f'A{row}'] = "源文件："
        ws[f'B{row}'] = os.path.basename(self.source_file)
        row += 1

        ws[f'A{row}'] = "总记录数："
        ws[f'B{row}'] = len(self.data)
        row += 2

        # 数据质量评分
        ws[f'A{row}'] = "数据质量评分"
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].fill = header_fill
        row += 1

        ws[f'A{row}'] = "评分："
        ws[f'B{row}'] = f"{self.validation_results['data_quality_score']}/100"
        row += 1

        ws[f'A{row}'] = "发现问题："
        ws[f'B{row}'] = f"{self.validation_results['total_issues']} 个"
        row += 2

        # 重复数据统计
        ws[f'A{row}'] = "重复数据统计"
        ws[f'A{row}'].font = header_font
        ws[f'A{row}'].fill = header_fill
        row += 1

        for field, groups in self.duplicate_groups.items():
            ws[f'A{row}'] = f"{field}字段："
            ws[f'B{row}'] = f"{len(groups)} 组重复"
            row += 1

        # 调整列宽
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 30

    def _create_validation_sheet(self, wb: Workbook):
        """创建数据验证工作表"""
        ws = wb.create_sheet("数据验证结果")

        # 标题
        ws['A1'] = "数据验证问题列表"
        ws['A1'].font = Font(size=12, bold=True)

        # 表头
        headers = ["序号", "问题类型", "问题描述", "严重程度"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=2, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E6E6FA", end_color="E6E6FA", fill_type="solid")

        # 写入验证问题
        row = 3
        for i, issue in enumerate(self.validation_results['issues'], 1):
            ws.cell(row=row, column=1, value=i)
            ws.cell(row=row, column=2, value="数据格式")
            ws.cell(row=row, column=3, value=issue)
            ws.cell(row=row, column=4, value="中等" if "科学计数法" in issue else "低")
            row += 1

        # 调整列宽
        ws.column_dimensions['A'].width = 8
        ws.column_dimensions['B'].width = 15
        ws.column_dimensions['C'].width = 50
        ws.column_dimensions['D'].width = 10

    def _create_duplicate_sheets(self, wb: Workbook):
        """创建重复数据工作表 - 增强版本（按字段分组）"""

        try:
            if not self.duplicate_groups:
                return

            # 为每个字段创建单独的工作表
            for field, groups in self.duplicate_groups.items():
                if not groups:
                    continue

                # 创建字段专用工作表
                sheet_name = self._clean_worksheet_name(f"{field}同源数据")
                ws = wb.create_sheet(sheet_name)

                # 收集该字段的所有重复数据
                field_duplicate_data = []

                # 按重复值分组并排序
                sorted_groups = sorted(groups, key=lambda g: g['clean_value'])

                for group in sorted_groups:
                    # 获取该组的所有记录
                    group_rows = self.data.loc[group['row_indices']].copy()

                    # 为每行添加重复信息
                    for idx in group['row_indices']:
                        if idx in group_rows.index:
                            row_data = group_rows.loc[idx].copy()
                            # 在前面插入重复信息列
                            row_dict = {
                                '重复字段': field,
                                '重复值': group['original_values'][0] if group['original_values'] else '',
                                '客户数量': group['customer_count'],
                                '客户身份': self._build_customer_identity(idx)
                            }
                            # 添加原始数据
                            for col, val in row_data.items():
                                row_dict[col] = val
                            field_duplicate_data.append(row_dict)

                if field_duplicate_data:
                    # 转换为DataFrame
                    field_df = pd.DataFrame(field_duplicate_data)

                    # 按重复值排序，确保相同值的记录相邻
                    field_df = field_df.sort_values(['重复值', '客户身份'])

                    # 写入工作表
                    self._write_dataframe_to_worksheet(ws, field_df, f"{field}字段同源数据")

            # 创建汇总工作表（保留原有功能）
            self._create_summary_duplicate_sheet(wb)

        except Exception as e:
            print(f"⚠️ 创建重复数据工作表时出错: {str(e)}")
            # 创建一个简单的错误说明工作表
            try:
                error_ws = wb.create_sheet("数据处理说明")
                error_ws.cell(row=1, column=1, value="重复数据包含特殊字符无法在Excel中显示")
                error_ws.cell(row=2, column=1, value="请查看CSV格式的备用报告获取完整数据")
                error_ws.cell(row=3, column=1, value=f"错误详情: {str(e)}")
            except:
                pass  # 如果连错误工作表都无法创建，就放弃

    def _write_dataframe_to_worksheet(self, ws, df, title):
        """将DataFrame安全地写入工作表"""
        try:
            # 写入标题
            ws.cell(row=1, column=1, value=title)
            ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(df.columns))
            ws.cell(row=1, column=1).font = Font(size=14, bold=True)

            # 写入表头
            for col_idx, header in enumerate(df.columns, 1):
                clean_header = self._clean_excel_string(str(header))
                cell = ws.cell(row=2, column=col_idx, value=clean_header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="E6E6FA", end_color="E6E6FA", fill_type="solid")

            # 写入数据行
            for row_idx, (_, row) in enumerate(df.iterrows(), 3):
                for col_idx, value in enumerate(row, 1):
                    try:
                        clean_value = self._clean_excel_string(str(value)) if pd.notna(value) else ""
                        if len(clean_value) > 32767:
                            clean_value = clean_value[:32764] + "..."
                        ws.cell(row=row_idx, column=col_idx, value=clean_value)
                    except Exception as cell_error:
                        ws.cell(row=row_idx, column=col_idx, value="数据包含无效字符")

            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        cell_value = str(cell.value) if cell.value is not None else ""
                        if len(cell_value) > max_length:
                            max_length = len(cell_value)
                    except:
                        pass
                adjusted_width = min(max(max_length + 2, 10), 50)
                ws.column_dimensions[column_letter].width = adjusted_width

        except Exception as e:
            print(f"⚠️ 写入工作表时出错: {str(e)}")

    def _create_summary_duplicate_sheet(self, wb: Workbook):
        """创建汇总的重复数据工作表"""
        try:
            ws_name = self._clean_worksheet_name("同源数据汇总")
            ws = wb.create_sheet(ws_name)

            # 收集所有重复数据行
            all_duplicate_rows = set()
            for field, groups in self.duplicate_groups.items():
                for group in groups:
                    all_duplicate_rows.update(group['row_indices'])

            if all_duplicate_rows:
                # 获取重复数据
                duplicate_data = self.data.loc[list(all_duplicate_rows)].copy()

                # 添加标识列
                duplicate_data.insert(0, '重复字段', '')
                duplicate_data.insert(1, '重复值', '')
                duplicate_data.insert(2, '客户数量', '')

                # 填充标识信息
                for field, groups in self.duplicate_groups.items():
                    for group in groups:
                        for idx in group['row_indices']:
                            if idx in duplicate_data.index:
                                duplicate_data.loc[idx, '重复字段'] = field
                                duplicate_data.loc[idx, '重复值'] = group['original_values'][0] if group['original_values'] else ''
                                duplicate_data.loc[idx, '客户数量'] = group['customer_count']

                # 按重复字段和重复值排序，确保相同值的记录相邻，与字段专用文件保持一致
                duplicate_data = duplicate_data.sort_values(['重复字段', '重复值'])

                # 写入工作表
                self._write_dataframe_to_worksheet(ws, duplicate_data, "同源数据汇总")

        except Exception as e:
            print(f"⚠️ 创建汇总工作表时出错: {str(e)}")


class IPLocationQuerier:
    """IP归属地查询器"""

    def __init__(self, source_file_path: str, progress_callback=None, log_callback=None, use_cache=True):
        """
        初始化IP归属地查询器

        Args:
            source_file_path: 同源数据结果文件路径
            progress_callback: 进度回调函数
            log_callback: 日志回调函数
            use_cache: 是否读取缓存（默认True）- 注意：缓存写入始终启用
        """
        self.source_file = source_file_path
        self.progress_callback = progress_callback
        self.log_callback = log_callback
        self.use_cache = use_cache  # 控制是否读取缓存
        self.data = None
        self.unique_ips = set()
        self.ip_location_cache = {}
        self.api_url = "https://apimobile.meituan.com/locate/v2/ip/loc?rgeo=true&ip={}"
        self.query_interval = 3  # 查询间隔（秒）
        self.max_queries_per_minute = 30  # 每分钟最大查询次数
        self.query_count = 0
        self.last_query_time = 0
        # 线程安全控制
        self.query_lock = threading.Lock()
        self.rate_limit_lock = threading.Lock()

        # 缓存相关设置
        self.cache_file_path = "ip_location_cache.json"
        self.persistent_cache = {}
        self.cache_write_lock = threading.Lock()  # 缓存写入锁
        self.cache_batch_size = 10  # 每N个查询结果批量写入缓存
        self.cache_batch_counter = 0  # 批量写入计数器

        # 始终加载缓存（无论use_cache设置如何）
        self._load_cache_from_file()

    def _log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def _update_progress(self, value, message):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(value, message)

    def _load_cache_from_file(self):
        """从文件加载IP地址缓存（始终执行，无论use_cache设置如何）"""
        try:
            if os.path.exists(self.cache_file_path):
                with open(self.cache_file_path, 'r', encoding='utf-8') as f:
                    loaded_cache = json.load(f)
                self._log(f"✅ 加载IP缓存文件: {len(loaded_cache)} 条记录")

                # 验证缓存数据格式
                valid_cache = {}
                for ip, location in loaded_cache.items():
                    if isinstance(ip, str) and isinstance(location, str):
                        # 标准化IP地址作为缓存键
                        normalized_ip = self._normalize_ip_for_cache(ip)
                        if normalized_ip:
                            valid_cache[normalized_ip] = location

                self.persistent_cache = valid_cache
                if len(valid_cache) != len(loaded_cache):
                    self._log(f"⚠️ 缓存数据清理: 保留 {len(valid_cache)} 条有效记录")
            else:
                self.persistent_cache = {}
                self._log("ℹ️ 未找到缓存文件，将创建新的缓存")
        except Exception as e:
            self._log(f"⚠️ 加载缓存文件失败: {str(e)}，使用空缓存")
            self.persistent_cache = {}

    def _save_cache_to_file(self):
        """保存IP地址缓存到文件（完整保存）"""
        try:
            # 合并内存缓存和持久化缓存
            combined_cache = self.persistent_cache.copy()
            for ip, location_info in self.ip_location_cache.items():
                normalized_ip = self._normalize_ip_for_cache(ip)
                if normalized_ip and location_info:
                    # 处理字典格式
                    if isinstance(location_info, dict):
                        location = location_info.get('location', '')
                        if location and location not in ["查询失败", "查询出错", "解析失败"]:
                            combined_cache[normalized_ip] = location_info
                    # 兼容旧格式字符串
                    elif isinstance(location_info, str) and location_info not in ["查询失败", "查询出错"]:
                        # 转换为新格式
                        combined_cache[normalized_ip] = {'location': location_info, 'coordinates': ''}

            # 原子性写入：先写入临时文件，再重命名
            temp_file = self.cache_file_path + ".tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(combined_cache, f, ensure_ascii=False, indent=2)

            # 原子性重命名
            if os.path.exists(temp_file):
                if os.path.exists(self.cache_file_path):
                    os.remove(self.cache_file_path)
                os.rename(temp_file, self.cache_file_path)

            self._log(f"✅ 保存IP缓存文件: {len(combined_cache)} 条记录")
            self.persistent_cache = combined_cache
        except Exception as e:
            self._log(f"❌ 保存缓存文件失败: {str(e)}")

    def _save_single_ip_to_cache(self, ip: str, location_info):
        """
        立即将单个IP查询结果保存到缓存（增量保存）

        Args:
            ip: IP地址
            location_info: 归属地信息（字典格式包含location和coordinates）
        """
        try:
            with self.cache_write_lock:
                normalized_ip = self._normalize_ip_for_cache(ip)
                if normalized_ip and location_info:
                    # 处理字典格式的location_info
                    if isinstance(location_info, dict):
                        location = location_info.get('location', '')
                        if location and location not in ["查询失败", "查询出错", "解析失败"]:
                            # 更新内存缓存
                            self.persistent_cache[normalized_ip] = location_info

                            # 增量计数器
                            self.cache_batch_counter += 1

                            # 每N个查询结果或重要查询立即写入文件
                            if (self.cache_batch_counter >= self.cache_batch_size or
                                self.cache_batch_counter % 5 == 0):  # 每5个也写入一次作为备份
                                self._write_cache_file_atomic()
                    # 兼容旧格式字符串
                    elif isinstance(location_info, str) and location_info not in ["查询失败", "查询出错"]:
                        # 转换为新格式
                        converted_info = {'location': location_info, 'coordinates': ''}
                        self.persistent_cache[normalized_ip] = converted_info
                        self.cache_batch_counter += 1
                        if (self.cache_batch_counter >= self.cache_batch_size or
                            self.cache_batch_counter % 5 == 0):
                            self._write_cache_file_atomic()

        except Exception as e:
            self._log(f"⚠️ 增量保存缓存失败: {str(e)}")

    def _write_cache_file_atomic(self):
        """原子性写入缓存文件"""
        try:
            # 原子性写入：先写入临时文件，再重命名
            temp_file = self.cache_file_path + ".tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(self.persistent_cache, f, ensure_ascii=False, indent=2)

            # 原子性重命名
            if os.path.exists(temp_file):
                if os.path.exists(self.cache_file_path):
                    os.remove(self.cache_file_path)
                os.rename(temp_file, self.cache_file_path)

        except Exception as e:
            # 清理临时文件
            temp_file = self.cache_file_path + ".tmp"
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            raise e

    def _normalize_ip_for_cache(self, ip_str: str) -> str:
        """
        标准化IP地址用作缓存键

        Args:
            ip_str: 原始IP地址字符串

        Returns:
            str: 标准化后的IP地址，如果无效则返回空字符串
        """
        if not ip_str:
            return ""

        # 使用现有的IP清理逻辑
        cleaned_ip = self._clean_ip_address(ip_str)
        if cleaned_ip and self._is_valid_ip(cleaned_ip):
            return cleaned_ip
        return ""

    def _get_cached_location(self, ip: str):
        """
        从缓存获取IP地址归属地

        Args:
            ip: IP地址

        Returns:
            dict or str: 归属地信息（新格式为字典，旧格式为字符串），如果未缓存则返回None
        """
        if not self.use_cache:
            return None

        normalized_ip = self._normalize_ip_for_cache(ip)
        if normalized_ip in self.persistent_cache:
            cached_data = self.persistent_cache[normalized_ip]
            # 如果是旧格式字符串，转换为新格式字典
            if isinstance(cached_data, str):
                return {'location': cached_data, 'coordinates': ''}
            return cached_data
        return None

    def get_cache_stats(self) -> dict:
        """
        获取缓存统计信息

        Returns:
            dict: 包含缓存统计信息的字典
        """
        return {
            'enabled': self.use_cache,
            'cache_file': self.cache_file_path,
            'cache_size': len(self.persistent_cache),
            'cache_exists': os.path.exists(self.cache_file_path)
        }

    def clear_cache(self):
        """清空缓存"""
        try:
            self.persistent_cache = {}
            if os.path.exists(self.cache_file_path):
                os.remove(self.cache_file_path)
            self._log("✅ IP地址缓存已清空")
        except Exception as e:
            self._log(f"❌ 清空缓存失败: {str(e)}")

    def finalize_cache(self):
        """
        最终化缓存 - 确保所有数据都已保存
        在程序结束或中断时调用
        """
        try:
            if hasattr(self, 'persistent_cache') and len(self.persistent_cache) > 0:
                self._write_cache_file_atomic()
                self._log(f"🔒 缓存最终化完成: {len(self.persistent_cache)} 条记录已保存")
        except Exception as e:
            self._log(f"⚠️ 缓存最终化失败: {str(e)}")

    def __del__(self):
        """析构函数 - 确保缓存在对象销毁时保存"""
        try:
            self.finalize_cache()
        except:
            pass  # 析构函数中不抛出异常

    def _is_valid_ip(self, ip_str: str) -> bool:
        """
        验证IP地址格式（支持IPv4和IPv6）

        Args:
            ip_str: IP地址字符串

        Returns:
            bool: 是否为有效IP地址
        """
        try:
            if not ip_str:
                return False

            # 验证IPv4或IPv6格式
            ipaddress.ip_address(ip_str)
            return True
        except (ValueError, ipaddress.AddressValueError):
            return False

    def _clean_ip_address(self, ip_str: str) -> str:
        """
        清理IP地址字符串，移除非IP字符，支持12位数字串解析和IPv6

        Args:
            ip_str: 原始IP地址字符串

        Returns:
            str: 清理后的IP地址
        """
        if not ip_str or pd.isna(ip_str):
            return ""

        # 转换为字符串并去除首尾空格
        ip_str = str(ip_str).strip()

        # 移除单引号
        if ip_str.startswith("'"):
            ip_str = ip_str[1:]

        # 先尝试标准IPv4模式
        ipv4_pattern = r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
        ipv4_match = re.search(ipv4_pattern, ip_str)
        if ipv4_match:
            candidate = ipv4_match.group(1)
            if self._is_valid_ipv4(candidate):
                return candidate

        # 尝试IPv6模式（支持完整IPv6格式）
        # IPv6可能包含::缩写形式，需要更精确的匹配
        ipv6_pattern = r'([0-9a-fA-F]{0,4}:){2,7}[0-9a-fA-F]{0,4}'
        ipv6_match = re.search(ipv6_pattern, ip_str)
        if ipv6_match:
            candidate = ipv6_match.group(0)
            # 验证IPv6格式
            try:
                ipaddress.IPv6Address(candidate)
                return candidate
            except (ValueError, ipaddress.AddressValueError):
                pass

        # 尝试IPv6简化形式（如fe80::开头）
        if '::' in ip_str:
            # 提取可能的IPv6地址
            ipv6_simple_pattern = r'([0-9a-fA-F]*::[0-9a-fA-F:]*)'
            ipv6_simple_match = re.search(ipv6_simple_pattern, ip_str)
            if ipv6_simple_match:
                candidate = ipv6_simple_match.group(1)
                try:
                    ipaddress.IPv6Address(candidate)
                    return candidate
                except (ValueError, ipaddress.AddressValueError):
                    pass

        # 尝试12位数字串解析（如'100127008112'）
        digit_pattern = r'^(\d{12})$'
        digit_match = re.match(digit_pattern, ip_str)
        if digit_match:
            digits = digit_match.group(1)
            try:
                # 分为4组：第1-3位、第4-6位、第7-9位、第10-12位
                part1 = str(int(digits[0:3]))   # 去除前导零
                part2 = str(int(digits[3:6]))   # 去除前导零
                part3 = str(int(digits[6:9]))   # 去除前导零
                part4 = str(int(digits[9:12]))  # 去除前导零

                # 组合成IPv4地址
                ip_candidate = f"{part1}.{part2}.{part3}.{part4}"

                # 验证生成的IP地址是否有效
                if self._is_valid_ipv4(ip_candidate):
                    return ip_candidate
                else:
                    self._log(f"   ⚠️ 12位数字串解析结果无效: {digits} -> {ip_candidate}")
                    return ""
            except (ValueError, IndexError) as e:
                self._log(f"   ⚠️ 12位数字串解析失败: {digits} - {str(e)}")
                return ""

        # 如果都不匹配，记录日志并返回空字符串
        if ip_str:  # 只对非空字符串记录日志
            self._log(f"   ⚠️ 无法解析IP地址: '{ip_str}'")

        return ""

    def _is_valid_ipv4(self, ip_str: str) -> bool:
        """
        验证IPv4地址格式

        Args:
            ip_str: IP地址字符串

        Returns:
            bool: 是否为有效IPv4地址
        """
        try:
            parts = ip_str.split('.')
            if len(parts) != 4:
                return False

            for part in parts:
                num = int(part)
                if num < 0 or num > 255:
                    return False

            return True
        except (ValueError, AttributeError):
            return False

    def _extract_unique_ips(self):
        """从数据中提取唯一的IP地址"""
        self._log("🔍 开始提取IP地址...")
        self._log(f"📋 数据文件列名: {list(self.data.columns)}")

        # 查找包含IP的列（增强的匹配逻辑）
        ip_columns = []
        for col in self.data.columns:
            col_lower = col.lower()
            col_original = col

            # 详细的列检测逻辑
            is_ip_column = False
            reason = ""

            # 修复IP检测逻辑：'重复值'列应该被识别为包含IP地址
            if '重复值' in col_original:
                # '重复值'列默认包含IP地址，无需额外的IP关键词检查
                is_ip_column = True
                reason = "包含'重复值'（默认IP列）"
            elif '重复' in col_original:
                # 对于'重复'列，仍需要IP相关关键词确认
                if 'iip' in col_lower:
                    is_ip_column = True
                    reason = "包含'重复'和'IIP'"
                elif 'ip' in col_lower and 'iip' not in col_lower:
                    is_ip_column = True
                    reason = "包含'重复'和'IP'（非IIP）"

            # 记录检测过程
            if is_ip_column:
                ip_columns.append(col_original)
                self._log(f"   ✅ 检测到IP列: '{col_original}' - {reason}")
            else:
                self._log(f"   ❌ 跳过列: '{col_original}' - 不符合IP列条件")

        if not ip_columns:
            self._log("⚠️ 未找到IP相关的重复值列")
            self._log("📋 列检测详情:")
            for col in self.data.columns:
                self._log(f"   - '{col}': 包含'重复值'={('重复值' in col)}, 包含'重复'={('重复' in col)}, 包含'IP'={('ip' in col.lower())}")
            return

        self._log(f"📊 找到IP相关列: {ip_columns}")

        # 提取所有IP地址（分块处理优化）
        all_ips = set()
        parse_stats = {'total': 0, 'valid': 0, 'invalid': 0, 'empty': 0}
        detailed_invalid = []
        chunk_size = 500  # 每批处理500个IP地址

        for col in ip_columns:
            self._log(f"   处理列: {col}")
            col_stats = {'total': 0, 'valid': 0, 'invalid': 0, 'empty': 0}

            # 获取列数据并转换为列表
            col_values = self.data[col].dropna().tolist()
            total_values = len(col_values)

            if total_values == 0:
                self._log(f"   ⚠️ 列 '{col}' 无有效数据")
                continue

            # 分块处理
            for chunk_start in range(0, total_values, chunk_size):
                chunk_end = min(chunk_start + chunk_size, total_values)
                chunk_values = col_values[chunk_start:chunk_end]

                # 更新进度
                progress_pct = int((chunk_end / total_values) * 100)
                batch_num = (chunk_start // chunk_size) + 1
                total_batches = (total_values + chunk_size - 1) // chunk_size
                self._log(f"     📦 处理IP批次 {batch_num}/{total_batches} ({progress_pct}%)")

                # 处理当前批次
                for value in chunk_values:
                    parse_stats['total'] += 1
                    col_stats['total'] += 1
                    value_str = str(value).strip()

                    if not value_str or value_str in ['', 'nan', 'None']:
                        parse_stats['empty'] += 1
                        col_stats['empty'] += 1
                        continue

                    cleaned_ip = self._clean_ip_address(value_str)
                    if cleaned_ip and self._is_valid_ip(cleaned_ip):
                        all_ips.add(cleaned_ip)
                        parse_stats['valid'] += 1
                        col_stats['valid'] += 1
                        # 减少详细日志输出，只记录少量示例
                        if col_stats['valid'] <= 5:
                            self._log(f"       ✅ 有效IP示例: '{value_str}' -> '{cleaned_ip}'")
                    else:
                        parse_stats['invalid'] += 1
                        col_stats['invalid'] += 1
                        detailed_invalid.append(f"'{value_str}' -> '{cleaned_ip}'")
                        if len(detailed_invalid) <= 5:  # 减少无效示例记录
                            self._log(f"       ❌ 无效IP示例: '{value_str}' -> '{cleaned_ip}'")

            self._log(f"   列 '{col}' 统计: 总计{col_stats['total']}, 有效{col_stats['valid']}, 无效{col_stats['invalid']}, 空值{col_stats['empty']}")

        self.unique_ips = all_ips
        self._log(f"✅ IP地址提取完成:")
        self._log(f"   - 总计处理: {parse_stats['total']} 个值")
        self._log(f"   - 有效IP: {parse_stats['valid']} 个")
        self._log(f"   - 无效数据: {parse_stats['invalid']} 个")
        self._log(f"   - 空值: {parse_stats['empty']} 个")
        self._log(f"   - 唯一IP: {len(self.unique_ips)} 个")

        if len(detailed_invalid) > 10:
            self._log(f"   - 更多无效IP示例: {len(detailed_invalid) - 10} 个（已省略）")

        if len(self.unique_ips) > 0:
            # 显示前几个IP作为示例
            sample_ips = list(self.unique_ips)[:5]
            self._log(f"📋 IP地址示例: {', '.join(sample_ips)}")
        else:
            self._log("⚠️ 未提取到有效的IP地址")

    def _query_ip_location(self, ip: str) -> str:
        """
        查询单个IP地址的归属地

        Args:
            ip: IP地址

        Returns:
            str: 归属地信息
        """
        import requests
        import time

        try:
            # 频率控制
            current_time = time.time()
            if current_time - self.last_query_time < self.query_interval:
                time.sleep(self.query_interval - (current_time - self.last_query_time))

            # 检查每分钟查询限制
            if self.query_count >= self.max_queries_per_minute:
                self._log("⏳ 达到每分钟查询限制，等待60秒...")
                time.sleep(60)
                self.query_count = 0

            # 发送查询请求
            url = self.api_url.format(ip)
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'cross-site'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # 更新查询统计
            self.query_count += 1
            self.last_query_time = time.time()

            # 解析JSON响应
            try:
                json_data = response.json()
                if 'data' in json_data and 'rgeo' in json_data['data']:
                    rgeo = json_data['data']['rgeo']
                    country = rgeo.get('country', '')
                    province = rgeo.get('province', '')
                    city = rgeo.get('city', '')
                    district = rgeo.get('district', '')
                    lng = json_data['data'].get('lng', '')
                    lat = json_data['data'].get('lat', '')

                    # 返回包含位置和坐标信息的字典
                    location_info = {
                        'location': f"{country}{province}{city}{district}",
                        'coordinates': f"{lng},{lat}" if lng and lat else ""
                    }
                    return location_info
                else:
                    return {'location': '未知', 'coordinates': ''}
            except (ValueError, KeyError) as e:
                self._log(f"⚠️ 解析IP {ip} 响应失败: {str(e)}")
                return {'location': '解析失败', 'coordinates': ''}

        except requests.exceptions.RequestException as e:
            self._log(f"⚠️ 查询IP {ip} 失败: {str(e)}")
            return "查询失败"
        except Exception as e:
            self._log(f"❌ 查询IP {ip} 出错: {str(e)}")
            return "查询出错"

    def _load_data(self):
        """加载同源数据文件"""
        self._log(f"📂 加载文件: {os.path.basename(self.source_file)}")

        try:
            if self.source_file.endswith('.xlsx'):
                # Excel文件，需要选择包含IP数据的工作表
                import openpyxl
                wb = openpyxl.load_workbook(self.source_file, read_only=True)

                # 查找包含IP字段的工作表
                target_sheet = None
                for sheet_name in wb.sheetnames:
                    if 'IIP' in sheet_name or 'IP' in sheet_name:
                        target_sheet = sheet_name
                        break

                if target_sheet:
                    self.data = pd.read_excel(self.source_file, sheet_name=target_sheet)
                    self._log(f"✅ 加载Excel工作表: {target_sheet}")
                else:
                    # 如果没有找到特定工作表，使用第一个工作表
                    self.data = pd.read_excel(self.source_file)
                    self._log("✅ 加载Excel文件（使用第一个工作表）")

            elif self.source_file.endswith('.csv'):
                self.data = pd.read_csv(
                    self.source_file,
                    encoding='utf-8-sig',
                    low_memory=False,
                    dtype=str
                )
                self._log("✅ 加载CSV文件")
            else:
                raise ValueError("不支持的文件格式，仅支持Excel(.xlsx)和CSV(.csv)格式")

            self._log(f"📊 数据行数: {len(self.data)}")
            self._log(f"📊 数据列数: {len(self.data.columns)}")

        except Exception as e:
            self._log(f"❌ 文件加载失败: {str(e)}")
            raise

    def _query_all_locations(self):
        """查询所有IP地址的归属地（支持缓存的优化版本）"""
        if not self.unique_ips:
            self._log("⚠️ 没有需要查询的IP地址")
            return

        total_ips = len(self.unique_ips)
        self._log(f"🌐 开始查询 {total_ips} 个IP地址的归属地...")

        # 检查缓存状态
        if self.use_cache:
            cached_count = 0
            need_query_ips = []

            for ip in self.unique_ips:
                cached_location = self._get_cached_location(ip)
                if cached_location:
                    self.ip_location_cache[ip] = cached_location
                    cached_count += 1
                else:
                    need_query_ips.append(ip)

            self._log(f"📋 缓存状态: {cached_count} 个IP已缓存, {len(need_query_ips)} 个需要查询")

            if cached_count > 0:
                self._log(f"⚡ 从缓存加载了 {cached_count} 个IP地址归属地")
        else:
            need_query_ips = list(self.unique_ips)
            self._log("🔄 缓存已禁用，将查询所有IP地址")

        # 如果没有需要查询的IP，直接返回
        if not need_query_ips:
            self._log("✅ 所有IP地址都已缓存，无需查询")
            return

        self._log(f"⏱️ 预计查询耗时: {len(need_query_ips) * self.query_interval / 60:.1f} 分钟")
        processed = 0

        # 使用线程池进行并发查询，但控制并发数量以遵守速率限制
        max_workers = 3  # 限制并发线程数以控制API调用频率

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有查询任务
            future_to_ip = {executor.submit(self._thread_safe_query_ip_location, ip): ip for ip in need_query_ips}

            # 处理完成的任务
            for future in as_completed(future_to_ip):
                ip = future_to_ip[future]
                processed += 1
                progress = int((processed / len(need_query_ips)) * 80) + 10  # 10-90%的进度范围

                try:
                    location = future.result()
                    self.ip_location_cache[ip] = location

                    # 立即保存到缓存（增量保存）
                    self._save_single_ip_to_cache(ip, location)

                    # 减少日志输出频率，只显示进度
                    if processed % 10 == 0 or processed == len(need_query_ips):
                        self._update_progress(progress, f"查询IP归属地 ({processed}/{len(need_query_ips)})")
                        self._log(f"🔍 已完成 {processed}/{len(need_query_ips)} 个IP查询")

                except Exception as e:
                    self._log(f"❌ 查询IP {ip} 失败: {str(e)}")
                    self.ip_location_cache[ip] = {'location': '查询失败', 'coordinates': ''}

        self._log(f"✅ IP归属地查询完成，共查询 {len(self.ip_location_cache)} 个IP")

        # 最终保存缓存到文件（确保所有数据都已持久化）
        if len(need_query_ips) > 0:
            try:
                self._write_cache_file_atomic()
                self._log(f"💾 最终缓存保存完成: {len(self.persistent_cache)} 条记录")
            except Exception as e:
                self._log(f"⚠️ 最终缓存保存失败: {str(e)}")

    def _thread_safe_query_ip_location(self, ip: str) -> str:
        """
        线程安全的IP地址归属地查询方法

        Args:
            ip: IP地址

        Returns:
            str: 归属地信息
        """
        import requests
        import time

        try:
            # 使用锁确保速率限制的线程安全
            with self.rate_limit_lock:
                # 频率控制
                current_time = time.time()
                if current_time - self.last_query_time < self.query_interval:
                    time.sleep(self.query_interval - (current_time - self.last_query_time))

                # 检查每分钟查询限制
                if self.query_count >= self.max_queries_per_minute:
                    self._log("⏳ 达到每分钟查询限制，等待60秒...")
                    time.sleep(60)
                    self.query_count = 0

                # 发送查询请求
                url = self.api_url.format(ip)
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'cross-site'
                }
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()

                # 更新查询统计
                self.query_count += 1
                self.last_query_time = time.time()

                # 解析JSON响应
                try:
                    json_data = response.json()
                    if 'data' in json_data and 'rgeo' in json_data['data']:
                        rgeo = json_data['data']['rgeo']
                        country = rgeo.get('country', '')
                        province = rgeo.get('province', '')
                        city = rgeo.get('city', '')
                        district = rgeo.get('district', '')
                        lng = json_data['data'].get('lng', '')
                        lat = json_data['data'].get('lat', '')

                        # 返回包含位置和坐标信息的字典
                        location_info = {
                            'location': f"{country}{province}{city}{district}",
                            'coordinates': f"{lng},{lat}" if lng and lat else ""
                        }
                        return location_info
                    else:
                        return {'location': '未知', 'coordinates': ''}
                except (ValueError, KeyError) as e:
                    return {'location': '解析失败', 'coordinates': ''}

        except requests.exceptions.RequestException as e:
            return {'location': f"查询失败: {str(e)}", 'coordinates': ''}
        except Exception as e:
            return {'location': f"查询出错: {str(e)}", 'coordinates': ''}

    def _add_location_column(self):
        """为数据添加IP归属地列"""
        self._log("📝 添加IP归属地列...")

        # 使用与_extract_unique_ips相同的列检测逻辑
        ip_columns = []
        for col in self.data.columns:
            col_lower = col.lower()
            col_original = col

            # 修复IP检测逻辑：'重复值'列应该被识别为包含IP地址
            if '重复值' in col_original:
                # '重复值'列默认包含IP地址，无需额外的IP关键词检查
                ip_columns.append(col_original)
            elif '重复' in col_original:
                # 对于'重复'列，仍需要IP相关关键词确认
                if 'iip' in col_lower or ('ip' in col_lower and 'iip' not in col_lower):
                    ip_columns.append(col_original)

        if not ip_columns:
            self._log("⚠️ 未找到IP相关的重复值列")
            return

        self._log(f"📊 为以下IP列添加归属地: {ip_columns}")

        # 为每个IP列添加对应的归属地列和坐标列
        for col in ip_columns:
            location_col = col.replace('重复值', 'IP地址归属地').replace('重复', 'IP地址归属地')
            coordinates_col = col.replace('重复值', '经纬度').replace('重复', '经纬度')

            # 创建归属地列和坐标列
            location_values = []
            coordinates_values = []
            for value in self.data[col]:
                if pd.isna(value):
                    location_values.append("")
                    coordinates_values.append("")
                else:
                    cleaned_ip = self._clean_ip_address(str(value))
                    if cleaned_ip in self.ip_location_cache:
                        cached_data = self.ip_location_cache[cleaned_ip]
                        # 处理新格式字典数据
                        if isinstance(cached_data, dict):
                            location_values.append(cached_data.get('location', ''))
                            coordinates_values.append(cached_data.get('coordinates', ''))
                        # 兼容旧格式字符串数据
                        else:
                            location_values.append(str(cached_data))
                            coordinates_values.append("")
                    else:
                        location_values.append("未查询")
                        coordinates_values.append("")

            # 在原列后面插入归属地列和坐标列
            col_index = self.data.columns.get_loc(col)
            self.data.insert(col_index + 1, location_col, location_values)
            self.data.insert(col_index + 2, coordinates_col, coordinates_values)

            self._log(f"✅ 添加列: {location_col}")
            self._log(f"✅ 添加列: {coordinates_col}")

    def _save_result(self):
        """保存带有IP归属地的结果文件"""
        # 生成输出文件名
        base_name, ext = os.path.splitext(self.source_file)
        output_file = f"{base_name}（带IP归属地）{ext}"

        self._log(f"💾 保存结果文件: {os.path.basename(output_file)}")

        try:
            if output_file.endswith('.xlsx'):
                self.data.to_excel(output_file, index=False)
            else:
                self.data.to_csv(output_file, index=False, encoding='utf-8-sig')

            self._log(f"✅ 结果文件已保存: {output_file}")
            return output_file

        except Exception as e:
            self._log(f"❌ 保存文件失败: {str(e)}")
            raise

    def run_query(self):
        """执行完整的IP归属地查询流程"""
        try:
            self._update_progress(5, "加载数据文件...")
            self._load_data()

            self._update_progress(10, "提取IP地址...")
            self._extract_unique_ips()

            if not self.unique_ips:
                self._log("⚠️ 未找到有效的IP地址，跳过查询")
                self._update_progress(100, "完成（无IP地址需要查询）")
                return None

            # 显示缓存状态信息
            cache_size = len(self.persistent_cache)
            self._log(f"📋 当前缓存状态: {cache_size} 个IP地址已缓存")

            if self.use_cache:
                self._log("✅ 缓存读取已启用，将优先使用已缓存的IP地址")
            else:
                self._log("🔄 缓存读取已禁用，将直接查询所有IP地址")

            self._log("💾 缓存写入始终启用，查询结果将实时保存到缓存")

            # 查询IP归属地（10-90%进度）
            self._query_all_locations()

            self._update_progress(90, "添加归属地信息...")
            self._add_location_column()

            self._update_progress(95, "保存结果文件...")
            output_file = self._save_result()

            # 显示最终缓存统计
            final_cache_size = len(self.persistent_cache)
            self._log(f"📊 缓存更新完成: 当前缓存包含 {final_cache_size} 个IP地址")

            self._update_progress(100, "IP归属地查询完成")
            return output_file

        except Exception as e:
            # 即使出现异常，也要尝试保存已获取的缓存数据
            try:
                if hasattr(self, 'persistent_cache') and len(self.persistent_cache) > 0:
                    self._write_cache_file_atomic()
                    self._log(f"🛡️ 异常处理：已保存 {len(self.persistent_cache)} 条缓存记录")
            except Exception as cache_error:
                self._log(f"⚠️ 异常处理中缓存保存失败: {str(cache_error)}")

            self._log(f"❌ IP归属地查询失败: {str(e)}")
            raise


class RiskAnalyzer:
    """风险信息识别分析器"""

    def __init__(self, source_file_path: str, progress_callback=None, log_callback=None):
        """
        初始化风险分析器

        Args:
            source_file_path: DBF合并后的原始文件路径
            progress_callback: 进度回调函数
            log_callback: 日志回调函数
        """
        self.source_file = source_file_path
        self.progress_callback = progress_callback
        self.log_callback = log_callback
        self.data = None
        self.risk_patterns = {
            'integer_transfer': '存在风险-整数或近似整数转账',
            'payday_transaction': '存在风险-临近发薪日交易',
            'month_end_transaction': '存在风险-月初月末交易',
            'password_trial_anomaly': '疑似他人操作-试密过程异常',
            'frequent_operation': '存在风险-单日内频繁操作',
            'regular_transfer': '存在风险-资金定期定额转出'
        }

    def _log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def _update_progress(self, value, message):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(value, message)

    def _load_data(self):
        """加载DBF合并文件"""
        self._log(f"📂 加载文件: {os.path.basename(self.source_file)}")

        try:
            if self.source_file.endswith('.parquet'):
                self.data = pd.read_parquet(self.source_file)
                self._log("✅ Parquet格式文件加载成功")
            elif self.source_file.endswith('.csv'):
                self.data = pd.read_csv(
                    self.source_file,
                    encoding='utf-8-sig',
                    low_memory=False,
                    dtype=str,
                    na_values=['', 'NA', 'N/A', 'null', 'NULL', 'None'],
                    keep_default_na=True
                )
                self._log("✅ CSV格式文件加载成功")
            else:
                raise ValueError("不支持的文件格式，仅支持CSV和Parquet格式")

            self._log(f"📊 数据行数: {len(self.data)}")
            self._log(f"📊 数据列数: {len(self.data.columns)}")

            # 检查必要的列是否存在
            required_columns = ['发生金额', '交易日期', '客户编号', '银行错误信']
            missing_columns = [col for col in required_columns if col not in self.data.columns]
            if missing_columns:
                self._log(f"⚠️ 缺少必要列: {missing_columns}")
                self._log("📋 可用列: " + ", ".join(self.data.columns.tolist()))

        except Exception as e:
            self._log(f"❌ 文件加载失败: {str(e)}")
            raise

    def _check_integer_transfer_risk(self, row):
        """检查整数或近似整数转账风险"""
        try:
            amount_str = str(row.get('发生金额', ''))
            if not amount_str or amount_str in ['', 'nan', 'None']:
                return False

            # 尝试转换为浮点数
            amount = float(amount_str)

            # 检查是否为10000的整数倍
            if amount > 0 and amount % 10000 == 0:
                return True

            # 检查是否大于10000且包含特定数字模式（移除"01"模式）
            if amount > 10000:
                # 移除小数点，检查数字模式
                amount_digits = str(int(amount))
                risk_patterns = ['999', '998', '001', '111', '222', '333', '444', '555', '666', '777', '888']
                for pattern in risk_patterns:
                    if pattern in amount_digits:
                        return True

            return False

        except (ValueError, TypeError):
            return False

    def _check_payday_transaction_risk(self, row):
        """检查发薪日临近交易风险"""
        try:
            amount_str = str(row.get('发生金额', ''))
            date_str = str(row.get('交易日期', ''))

            if not amount_str or not date_str or amount_str in ['', 'nan', 'None'] or date_str in ['', 'nan', 'None']:
                return False

            amount = float(amount_str)
            if amount < 10000:
                return False

            # 解析日期（YYYYMMDD格式）
            if len(date_str) >= 8:
                day = int(date_str[-2:])  # 取最后两位作为日期
                return day in [10, 11, 15, 16]

            return False

        except (ValueError, TypeError):
            return False

    def _check_month_end_transaction_risk(self, row):
        """检查月初月末交易风险"""
        try:
            amount_str = str(row.get('发生金额', ''))
            date_str = str(row.get('交易日期', ''))

            if not amount_str or not date_str or amount_str in ['', 'nan', 'None'] or date_str in ['', 'nan', 'None']:
                return False

            amount = float(amount_str)
            if amount < 10000:
                return False

            # 解析日期（YYYYMMDD格式）
            if len(date_str) >= 8:
                day = int(date_str[-2:])  # 取最后两位作为日期
                return day in [1, 2, 3, 29, 30, 31]

            return False

        except (ValueError, TypeError):
            return False

    def _check_password_trial_anomaly(self, customer_date_group):
        """检查试密过程异常风险"""
        try:
            # 获取该客户该日期的所有银行错误信息
            error_messages = customer_date_group['银行错误信'].dropna().astype(str).tolist()

            if not error_messages:
                return False

            # 将所有错误信息拼接
            combined_messages = ' '.join(error_messages)

            # 检查是否同时包含成功和密码关键词
            success_keywords = ['交易成功', '转账成功']
            password_keywords = ['密码']

            has_success = any(keyword in combined_messages for keyword in success_keywords)
            has_password = any(keyword in combined_messages for keyword in password_keywords)

            return has_success and has_password

        except Exception:
            return False

    def _check_frequent_operation_risk(self, customer_date_group):
        """检查频繁操作风险"""
        try:
            # 检查同一客户同一日期的记录数是否超过15条
            return len(customer_date_group) > 15
        except Exception:
            return False

    def _check_regular_transfer_risk(self):
        """检查资金定期定额转出风险（高性能优化版本）"""
        try:
            self._log("📊 开始检查资金定期定额转出风险...")

            if '客户编号' not in self.data.columns or '交易日期' not in self.data.columns or '发生金额' not in self.data.columns:
                self._log("⚠️ 缺少必要列，跳过定期转账风险检查")
                return {}

            # 数据预处理和清洗
            self._log("   🔄 数据预处理...")
            df = self.data.copy()

            # 转换数据类型
            df['发生金额'] = pd.to_numeric(df['发生金额'], errors='coerce')
            df['交易日期'] = df['交易日期'].astype(str)

            # 过滤无效数据
            df = df.dropna(subset=['客户编号', '交易日期', '发生金额'])
            df = df[df['发生金额'] >= 10000]  # 预先过滤小额交易
            df = df[df['交易日期'].str.len() >= 8]  # 确保日期格式正确

            if len(df) == 0:
                self._log("⚠️ 过滤后无有效数据")
                return {}

            self._log(f"   📊 预处理后数据量: {len(df)} 条（原始: {len(self.data)} 条）")

            # 解析日期字段
            df['年'] = df['交易日期'].str[:4].astype(int)
            df['月'] = df['交易日期'].str[4:6].astype(int)
            df['日'] = df['交易日期'].str[6:8].astype(int)
            df['年月'] = df['年'] * 100 + df['月']

            # 进一步过滤：只保留有足够交易记录的客户
            customer_counts = df['客户编号'].value_counts()
            valid_customers = customer_counts[customer_counts >= 3].index
            df = df[df['客户编号'].isin(valid_customers)]

            if len(df) == 0:
                self._log("⚠️ 没有客户有足够的交易记录")
                return {}

            self._log(f"   📊 有效客户数: {len(valid_customers)}")

            # 使用优化的检测逻辑
            regular_transfer_records = self._find_regular_patterns_optimized(df)

            self._log(f"✅ 定期转账风险检查完成，发现 {len(regular_transfer_records)} 条记录")
            return regular_transfer_records

        except Exception as e:
            self._log(f"❌ 定期转账风险检查失败: {str(e)}")
            return {}

    def _find_regular_patterns_optimized(self, df):
        """使用高度优化的算法查找定期转账模式"""
        regular_transfer_records = {}

        # 获取唯一客户列表并分批处理
        unique_customers = df['客户编号'].unique()
        total_customers = len(unique_customers)

        # 根据数据量动态调整批次大小
        if total_customers > 10000:
            chunk_size = 200  # 大数据集使用较大批次
        elif total_customers > 1000:
            chunk_size = 100  # 中等数据集
        else:
            chunk_size = 50   # 小数据集

        self._log(f"   📊 开始分批处理 {total_customers} 个客户，批次大小: {chunk_size}")

        processed_customers = 0

        # 分批处理客户以避免内存问题
        for i in range(0, total_customers, chunk_size):
            customer_batch = unique_customers[i:i + chunk_size]
            batch_df = df[df['客户编号'].isin(customer_batch)].copy()

            # 内存监控
            import psutil
            memory_usage = psutil.virtual_memory().percent
            if memory_usage > 85:
                self._log(f"   ⚠️ 内存使用率较高 ({memory_usage:.1f}%)，强制垃圾回收")
                import gc
                gc.collect()

            # 处理当前批次
            batch_results = self._process_customer_batch_vectorized(batch_df)
            regular_transfer_records.update(batch_results)

            processed_customers += len(customer_batch)
            progress = int((processed_customers / total_customers) * 100)

            # 更新进度（映射到70-85%范围内）
            overall_progress = 70 + int(progress * 0.15)
            self._update_progress(overall_progress, f"定期转账检查: {processed_customers}/{total_customers} 客户")

            self._log(f"   📈 已处理 {processed_customers}/{total_customers} 个客户 ({progress}%)")

            # 强制垃圾回收以释放内存
            del batch_df
            import gc
            gc.collect()

        return regular_transfer_records

    def _process_customer_batch_vectorized(self, batch_df):
        """使用向量化操作处理客户批次"""
        results = {}

        # 预计算金额范围和日期范围以减少重复计算
        batch_df['金额范围'] = (batch_df['发生金额'] // 10000) * 10000
        batch_df['日期范围'] = (batch_df['日'] // 3) * 3

        # 预过滤：只保留有足够记录的客户
        customer_counts = batch_df['客户编号'].value_counts()
        valid_customers = customer_counts[customer_counts >= 3].index

        if len(valid_customers) == 0:
            return results

        filtered_df = batch_df[batch_df['客户编号'].isin(valid_customers)]

        # 按客户分组，但使用更高效的方式
        for customer_id in valid_customers:
            customer_data = filtered_df[filtered_df['客户编号'] == customer_id]

            # 使用向量化方法查找模式
            customer_results = self._find_patterns_for_customer_vectorized(customer_data)
            results.update(customer_results)

        return results

    def _find_patterns_for_customer_vectorized(self, customer_data):
        """为单个客户使用向量化方法查找模式"""
        results = {}

        try:
            # 按金额范围和日期范围分组
            grouped = customer_data.groupby(['金额范围', '日期范围'])

            for (amount_range, day_range), group in grouped:
                _ = amount_range, day_range  # 避免未使用变量警告
                if len(group) < 3:
                    continue

                # 按年月分组并排序
                monthly_data = group.groupby('年月')
                months = sorted(monthly_data.groups.keys())

                # 使用滑动窗口查找连续三个月
                for i in range(len(months) - 2):
                    month1, month2, month3 = months[i], months[i+1], months[i+2]

                    if self._is_consecutive_months_simple(month1, month2, month3):
                        # 获取每个月的数据
                        data1 = monthly_data.get_group(month1)
                        data2 = monthly_data.get_group(month2)
                        data3 = monthly_data.get_group(month3)

                        # 查找最佳匹配的记录组合
                        best_match = self._find_best_match_vectorized(data1, data2, data3)

                        if best_match:
                            for idx in best_match['indices']:
                                results[idx] = best_match

        except Exception as e:
            # 记录错误但不中断整个处理流程
            customer_id = customer_data['客户编号'].iloc[0] if len(customer_data) > 0 else "未知"
            self._log(f"   ⚠️ 处理客户 {customer_id} 时出错: {str(e)}")

        return results

    def _find_best_match_vectorized(self, data1, data2, data3):
        """使用向量化方法查找最佳匹配的记录组合"""
        # 快速检查：确保每个月都有数据
        if len(data1) == 0 or len(data2) == 0 or len(data3) == 0:
            return None

        # 为了性能考虑，优先选择每个月金额最接近的记录
        # 如果只有一条记录，直接使用；如果有多条，选择金额最稳定的

        def select_best_record(month_data):
            if len(month_data) == 1:
                return month_data.iloc[0]
            # 选择金额最接近该月平均值的记录
            mean_amount = month_data['发生金额'].mean()
            closest_idx = (month_data['发生金额'] - mean_amount).abs().idxmin()
            return month_data.loc[closest_idx]

        try:
            row1 = select_best_record(data1)
            row2 = select_best_record(data2)
            row3 = select_best_record(data3)

            # 快速验证日期匹配（±1天容差）
            day1, day2, day3 = row1['日'], row2['日'], row3['日']

            # 使用更高效的日期差值计算
            if (abs(day1 - day2) <= 1 and
                abs(day2 - day3) <= 1 and
                abs(day1 - day3) <= 1):

                amounts = [row1['发生金额'], row2['发生金额'], row3['发生金额']]

                if self._amounts_match(amounts):
                    return {
                        'indices': [row1.name, row2.name, row3.name],
                        'dates': [row1['交易日期'], row2['交易日期'], row3['交易日期']],
                        'amounts': amounts,
                        'days': [day1, day2, day3]
                    }
        except Exception:
            # 如果出现任何错误，返回None而不中断处理
            return None

        return None

    def _is_consecutive_months_simple(self, month1, month2, month3):
        """检查是否为连续三个月（简化版本）"""
        # 转换为年和月
        year1, m1 = month1 // 100, month1 % 100
        year2, m2 = month2 // 100, month2 % 100
        year3, m3 = month3 // 100, month3 % 100

        # 转换为总月数
        total1 = year1 * 12 + m1
        total2 = year2 * 12 + m2
        total3 = year3 * 12 + m3

        return (total2 == total1 + 1) and (total3 == total2 + 1)



    def _amounts_match(self, amounts):
        """检查金额是否匹配（相同或上下浮动10000元内，且所有金额都≥10000）"""
        if len(amounts) != 3:
            return False

        # 新增要求：所有三笔交易金额都必须≥10000元
        if any(amount < 10000 for amount in amounts):
            return False

        min_amount = min(amounts)
        max_amount = max(amounts)

        # 检查最大值和最小值的差是否在10000元内
        return (max_amount - min_amount) <= 10000

    def _analyze_risks(self):
        """分析所有风险模式（增强版日志）"""
        import time
        start_time = time.time()

        self._log("🔍 步骤 1/6: 开始风险信息识别...")
        self._log(f"📊 数据规模: {len(self.data)} 条记录")

        # 初始化风险信息列
        self.data['风险信息'] = ''

        total_rows = len(self.data)
        risk_counts = {pattern: 0 for pattern in self.risk_patterns.values()}

        # 1. 检查单行风险（整数转账、发薪日交易、月初月末交易）
        self._log("📊 步骤 2/6: 检查单笔交易风险...")
        self._update_progress(20, "步骤 2/6: 检查单笔交易风险...")

        step_start_time = time.time()
        processed_rows = 0

        for idx, row in self.data.iterrows():
            risks = []

            # 整数或近似整数转账
            if self._check_integer_transfer_risk(row):
                risks.append(self.risk_patterns['integer_transfer'])
                risk_counts[self.risk_patterns['integer_transfer']] += 1

            # 发薪日临近交易
            if self._check_payday_transaction_risk(row):
                risks.append(self.risk_patterns['payday_transaction'])
                risk_counts[self.risk_patterns['payday_transaction']] += 1

            # 月初月末交易
            if self._check_month_end_transaction_risk(row):
                risks.append(self.risk_patterns['month_end_transaction'])
                risk_counts[self.risk_patterns['month_end_transaction']] += 1

            # 合并风险信息
            if risks:
                self.data.at[idx, '风险信息'] = '; '.join(risks)

            processed_rows += 1

            # 每处理1000行显示一次进度
            if processed_rows % 1000 == 0 or processed_rows == total_rows:
                progress_pct = int((processed_rows / total_rows) * 100)
                elapsed_time = time.time() - step_start_time
                if processed_rows > 0:
                    avg_time_per_row = elapsed_time / processed_rows
                    remaining_rows = total_rows - processed_rows
                    estimated_remaining = avg_time_per_row * remaining_rows
                    self._log(f"   📈 分析进度: {processed_rows}/{total_rows} ({progress_pct}%) - 预计剩余: {estimated_remaining:.1f}秒")

        step_elapsed = time.time() - step_start_time
        self._log(f"✅ 步骤 2/6 完成 - 耗时: {step_elapsed:.1f}秒")

        # 2. 检查客户级别风险（试密异常、频繁操作）
        self._log("📊 步骤 3/6: 检查客户级别风险...")
        self._update_progress(60, "步骤 3/6: 检查客户级别风险...")

        step_start_time = time.time()

        # 按客户编号和交易日期分组
        if '客户编号' in self.data.columns and '交易日期' in self.data.columns:
            grouped = self.data.groupby(['客户编号', '交易日期'])
            total_groups = len(grouped)
            processed_groups = 0

            self._log(f"   📋 需要分析 {total_groups} 个客户-日期组合")

            for (customer_id, trade_date), group in grouped:
                _ = trade_date  # 避免未使用变量警告
                group_indices = group.index.tolist()

                # 检查试密过程异常
                if self._check_password_trial_anomaly(group):
                    for idx in group_indices:
                        current_risk = self.data.at[idx, '风险信息']
                        new_risk = self.risk_patterns['password_trial_anomaly']
                        if current_risk:
                            self.data.at[idx, '风险信息'] = f"{current_risk}; {new_risk}"
                        else:
                            self.data.at[idx, '风险信息'] = new_risk
                    risk_counts[self.risk_patterns['password_trial_anomaly']] += len(group_indices)

                # 检查频繁操作
                if self._check_frequent_operation_risk(group):
                    for idx in group_indices:
                        current_risk = self.data.at[idx, '风险信息']
                        new_risk = self.risk_patterns['frequent_operation']
                        if current_risk:
                            self.data.at[idx, '风险信息'] = f"{current_risk}; {new_risk}"
                        else:
                            self.data.at[idx, '风险信息'] = new_risk
                    risk_counts[self.risk_patterns['frequent_operation']] += len(group_indices)

                processed_groups += 1

                # 每处理100个组合显示一次进度
                if processed_groups % 100 == 0 or processed_groups == total_groups:
                    progress_pct = int((processed_groups / total_groups) * 100)
                    elapsed_time = time.time() - step_start_time
                    if processed_groups > 0:
                        avg_time_per_group = elapsed_time / processed_groups
                        remaining_groups = total_groups - processed_groups
                        estimated_remaining = avg_time_per_group * remaining_groups
                        self._log(f"   📈 客户分析进度: {processed_groups}/{total_groups} ({progress_pct}%) - 预计剩余: {estimated_remaining:.1f}秒")
        else:
            self._log("   ⚠️ 缺少必要列，跳过客户级别风险检查")

        step_elapsed = time.time() - step_start_time
        self._log(f"✅ 步骤 3/6 完成 - 耗时: {step_elapsed:.1f}秒")

        # 3. 检查定期转账风险（已优化性能）
        self._log("📊 步骤 4/6: 检查定期转账风险...")
        self._update_progress(70, "步骤 4/6: 检查定期转账风险...")

        step_start_time = time.time()
        regular_transfer_records = self._check_regular_transfer_risk()
        step_elapsed = time.time() - step_start_time
        self._log(f"✅ 步骤 4/6 完成 - 耗时: {step_elapsed:.1f}秒，发现 {len(regular_transfer_records)} 条定期转账风险")

        # 应用定期转账风险标记
        self._log("📊 步骤 5/6: 应用风险标记...")
        self._update_progress(80, "步骤 5/6: 应用风险标记...")

        step_start_time = time.time()
        for idx, pattern in regular_transfer_records.items():
            _ = pattern  # 避免未使用变量警告
            current_risk = self.data.at[idx, '风险信息']
            new_risk = self.risk_patterns['regular_transfer']
            if current_risk:
                self.data.at[idx, '风险信息'] = f"{current_risk}; {new_risk}"
            else:
                self.data.at[idx, '风险信息'] = new_risk
            risk_counts[self.risk_patterns['regular_transfer']] += 1

        step_elapsed = time.time() - step_start_time
        self._log(f"✅ 步骤 5/6 完成 - 耗时: {step_elapsed:.1f}秒")

        self._update_progress(85, "步骤 6/6: 生成风险统计...")

        # 统计风险识别结果
        self._log("📊 步骤 6/6: 生成风险识别统计...")
        total_risk_records = len(self.data[self.data['风险信息'] != ''])
        total_elapsed = time.time() - start_time

        self._log(f"✅ 风险识别完成！总耗时: {total_elapsed:.1f}秒")
        self._log(f"📊 风险识别统计:")
        self._log(f"   📋 总记录数: {len(self.data)} 条")
        self._log(f"   ⚠️ 风险记录数: {total_risk_records} 条")
        self._log(f"   📈 风险比例: {(total_risk_records/len(self.data)*100):.2f}%")
        self._log(f"   ⚡ 处理速度: {len(self.data)/total_elapsed:.1f} 条/秒")

        self._log("📋 各类风险详细统计:")
        for risk_type, count in risk_counts.items():
            if count > 0:
                percentage = (count / total_risk_records * 100) if total_risk_records > 0 else 0
                self._log(f"   - {risk_type}: {count} 条 ({percentage:.1f}%)")

    def _save_risk_result(self):
        """保存风险识别结果"""
        # 只保留有风险信息的记录
        risk_data = self.data[self.data['风险信息'] != ''].copy()

        if len(risk_data) == 0:
            self._log("⚠️ 未发现任何风险记录")
            return None

        # 将风险信息列移动到第一列
        if '风险信息' in risk_data.columns:
            # 获取所有列名
            columns = list(risk_data.columns)
            # 移除风险信息列
            columns.remove('风险信息')
            # 将风险信息列放在第一位
            new_columns = ['风险信息'] + columns
            # 重新排列列顺序
            risk_data = risk_data[new_columns]
            self._log("📋 已将风险信息列移动到第一列")

        # 生成输出文件名
        base_name, ext = os.path.splitext(self.source_file)
        output_file = f"{base_name}_风险信息排查{ext}"

        self._log(f"💾 保存风险排查结果: {os.path.basename(output_file)}")
        self._log(f"📊 风险记录数: {len(risk_data)}")

        try:
            if output_file.endswith('.parquet'):
                risk_data.to_parquet(output_file, index=False)
            else:
                risk_data.to_csv(output_file, index=False, encoding='utf-8-sig')

            self._log(f"✅ 风险排查结果已保存: {output_file}")
            return output_file

        except Exception as e:
            self._log(f"❌ 保存文件失败: {str(e)}")
            raise

    def run_analysis(self):
        """执行完整的风险分析流程（增强版日志）"""
        import time
        start_time = time.time()

        try:
            self._log("🚀 开始风险分析流程...")
            self._log("=" * 60)

            # 步骤1: 加载数据
            self._log("📂 阶段 1/3: 数据加载")
            self._update_progress(10, "阶段 1/3: 加载数据文件...")
            step_start = time.time()
            self._load_data()
            step_elapsed = time.time() - step_start
            self._log(f"✅ 阶段 1/3 完成 - 耗时: {step_elapsed:.1f}秒")

            # 步骤2: 风险识别
            self._log("\n🔍 阶段 2/3: 风险识别分析")
            self._update_progress(20, "阶段 2/3: 开始风险识别...")
            step_start = time.time()
            self._analyze_risks()
            step_elapsed = time.time() - step_start
            self._log(f"✅ 阶段 2/3 完成 - 耗时: {step_elapsed:.1f}秒")

            # 步骤3: 保存结果
            self._log("\n💾 阶段 3/3: 保存结果")
            self._update_progress(90, "阶段 3/3: 保存风险排查结果...")
            step_start = time.time()
            output_file = self._save_risk_result()
            step_elapsed = time.time() - step_start
            self._log(f"✅ 阶段 3/3 完成 - 耗时: {step_elapsed:.1f}秒")

            # 总结
            total_elapsed = time.time() - start_time
            self._log("\n" + "=" * 60)
            self._log(f"🎉 风险分析流程全部完成！总耗时: {total_elapsed:.1f}秒")
            if output_file:
                self._log(f"📄 结果文件: {os.path.basename(output_file)}")
            self._log("=" * 60)

            self._update_progress(100, "风险排查完成")
            return output_file

        except Exception as e:
            total_elapsed = time.time() - start_time
            self._log(f"❌ 风险分析失败: {str(e)} (耗时: {total_elapsed:.1f}秒)")
            raise


class RiskScreeningProcessor:
    """风险排查处理器 - 整合IP归属地查询和风险信息识别"""

    def __init__(self, ip_source_file: str, risk_analysis_file: str,
                 progress_callback=None, log_callback=None):
        """
        初始化风险排查处理器

        Args:
            ip_source_file: IP同源数据文件路径
            risk_analysis_file: DBF合并文件路径
            progress_callback: 进度回调函数
            log_callback: 日志回调函数
        """
        self.ip_source_file = ip_source_file
        self.risk_analysis_file = risk_analysis_file
        self.progress_callback = progress_callback
        self.log_callback = log_callback
        self.results = {
            'ip_location_file': None,
            'risk_analysis_file': None
        }

    def _log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def _update_progress(self, value, message):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(value, message)

    def run_screening(self):
        """执行完整的风险排查流程"""
        try:
            self._log("🔍 开始风险排查处理...")
            self._log("=" * 60)

            # 第一阶段：IP归属地查询（0-50%进度）
            if self.ip_source_file:
                self._log("🌐 第一阶段：IP归属地查询")
                self._update_progress(5, "开始IP归属地查询...")

                def ip_progress_callback(value, message):
                    # 将IP查询的进度映射到0-50%范围
                    adjusted_value = int(value * 0.5)
                    self._update_progress(adjusted_value, f"IP归属地查询: {message}")

                ip_querier = IPLocationQuerier(
                    self.ip_source_file,
                    progress_callback=ip_progress_callback,
                    log_callback=self.log_callback
                )

                self.results['ip_location_file'] = ip_querier.run_query()

                if self.results['ip_location_file']:
                    self._log(f"✅ IP归属地查询完成: {os.path.basename(self.results['ip_location_file'])}")
                else:
                    self._log("⚠️ IP归属地查询未生成结果文件")
            else:
                self._log("⚠️ 跳过IP归属地查询（未提供IP同源数据文件）")
                self._update_progress(50, "跳过IP归属地查询")

            # 第二阶段：风险信息识别（50-100%进度）
            if self.risk_analysis_file:
                self._log("\n⚠️ 第二阶段：风险信息识别")
                self._update_progress(55, "开始风险信息识别...")

                def risk_progress_callback(value, message):
                    # 将风险分析的进度映射到50-100%范围
                    adjusted_value = 50 + int(value * 0.5)
                    self._update_progress(adjusted_value, f"风险信息识别: {message}")

                risk_analyzer = RiskAnalyzer(
                    self.risk_analysis_file,
                    progress_callback=risk_progress_callback,
                    log_callback=self.log_callback
                )

                self.results['risk_analysis_file'] = risk_analyzer.run_analysis()

                if self.results['risk_analysis_file']:
                    self._log(f"✅ 风险信息识别完成: {os.path.basename(self.results['risk_analysis_file'])}")
                else:
                    self._log("⚠️ 风险信息识别未生成结果文件")
            else:
                self._log("⚠️ 跳过风险信息识别（未提供DBF合并文件）")
                self._update_progress(100, "跳过风险信息识别")

            # 总结结果
            self._log("\n" + "=" * 60)
            self._log("🎉 风险排查处理完成！")
            self._log("📋 处理结果:")

            if self.results['ip_location_file']:
                self._log(f"   🌐 IP归属地查询结果: {os.path.basename(self.results['ip_location_file'])}")

            if self.results['risk_analysis_file']:
                self._log(f"   ⚠️ 风险信息识别结果: {os.path.basename(self.results['risk_analysis_file'])}")

            if not any(self.results.values()):
                self._log("   ⚠️ 未生成任何结果文件")

            self._update_progress(100, "风险排查完成")
            return self.results

        except Exception as e:
            self._log(f"❌ 风险排查处理失败: {str(e)}")
            raise


def main():
    """主入口函数"""
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--console':
        # 控制台模式
        merger = DBFMergerPro()
        merger.run_console_mode()
    else:
        # 默认GUI模式
        merger = DBFMergerPro()
        merger.run_gui_mode()

if __name__ == "__main__":
    main()